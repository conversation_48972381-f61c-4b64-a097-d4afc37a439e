#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
浏览器管理模块 - Selenium版本
"""

import asyncio
import json
import os
import time
import random
import threading
from pathlib import Path
from typing import Dict, Optional, Any, List
from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor

from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options as ChromeOptions
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import WebDriverException, TimeoutException
from webdriver_manager.chrome import ChromeDriverManager
import undetected_chromedriver as uc
from fake_useragent import UserAgent

# 🔧 关键修复：彻底隔离 undetected-chromedriver，支持大规模并发
import os
import tempfile
import shutil
from pathlib import Path

# 创建项目独立的chromedriver环境
_project_driver_dir = Path(__file__).parent.parent.parent / "data" / "chromedriver"
_project_driver_dir.mkdir(parents=True, exist_ok=True)

# 设置多个环境变量，确保完全隔离
os.environ['UC_DRIVER_PATH'] = str(_project_driver_dir)
os.environ['UNDETECTED_CHROMEDRIVER_PATH'] = str(_project_driver_dir)

# 创建临时目录作为APPDATA，避免冲突
_temp_appdata = _project_driver_dir / "temp_appdata"
_temp_appdata.mkdir(exist_ok=True)
os.environ['APPDATA'] = str(_temp_appdata)

from src.database.models import Account
from src.utils.logger import LoggerMixin
from src.config.settings import get_settings
from src.core.anti_detection import AntiDetectionEngine


class SeleniumDriverWrapper:
    """Selenium WebDriver包装器，提供异步接口"""

    def __init__(self, driver: webdriver.Chrome, account_id: int):
        self.driver = driver
        self.account_id = account_id
        self.executor = ThreadPoolExecutor(max_workers=1)
        self._closed = False

    async def execute_async(self, func, *args, **kwargs):
        """异步执行同步函数"""
        if self._closed:
            raise WebDriverException("Driver已关闭")

        try:
            loop = asyncio.get_event_loop()
            return await loop.run_in_executor(self.executor, func, *args, **kwargs)
        except Exception as e:
            # 检查是否是会话相关的错误
            error_msg = str(e).lower()
            if any(keyword in error_msg for keyword in [
                'invalid session id', 'session deleted', 'disconnected',
                'not connected to devtools', 'chrome not reachable'
            ]):
                self._closed = True
                raise WebDriverException("Driver会话已断开")
            raise

    async def get(self, url: str):
        """导航到URL"""
        return await self.execute_async(self.driver.get, url)

    async def find_element(self, by: By, value: str):
        """查找元素"""
        return await self.execute_async(self.driver.find_element, by, value)

    async def find_elements(self, by: By, value: str):
        """查找多个元素"""
        return await self.execute_async(self.driver.find_elements, by, value)

    async def execute_script(self, script: str, *args):
        """执行JavaScript"""
        return await self.execute_async(self.driver.execute_script, script, *args)

    async def is_session_valid(self) -> bool:
        """检查会话是否有效 - 宽松版本，避免误判"""
        if self._closed:
            return False

        try:
            # 使用更宽松的检查策略，避免误判有效的WebDriver
            # 方法1: 检查当前URL（主要检查）
            try:
                current_url = await self.execute_async(lambda: self.driver.current_url)
                # 只要能获取到URL且不是空白页就认为有效
                if current_url and current_url != 'data:,' and 'about:blank' not in current_url:
                    return True
            except Exception as e:
                # URL检查失败，尝试其他方法
                pass

            # 方法2: 检查窗口句柄（备用检查）
            try:
                handles = await self.execute_async(lambda: self.driver.window_handles)
                if handles and len(handles) > 0:
                    return True
            except Exception as e:
                # 窗口句柄检查失败，尝试最后的方法
                pass

            # 方法3: 执行简单的JavaScript（最后检查）
            try:
                result = await self.execute_async(
                    lambda: self.driver.execute_script("return 'test'")
                )
                if result == 'test':
                    return True
            except Exception as e:
                # 所有检查都失败，认为无效
                pass

            return False

        except Exception as e:
            # 检查是否是会话相关的错误
            error_msg = str(e).lower()
            if any(keyword in error_msg for keyword in [
                'invalid session id', 'session deleted', 'disconnected',
                'not connected to devtools', 'chrome not reachable',
                'driver已关闭', 'driver会话已断开'
            ]):
                self._closed = True
            return False

    async def close(self):
        """关闭驱动"""
        if not self._closed:
            self._closed = True
            await self.execute_async(self.driver.quit)
            self.executor.shutdown(wait=True)

    @property
    def current_url(self) -> str:
        """当前URL"""
        return self.driver.current_url

    @property
    def page_source(self) -> str:
        """页面源码"""
        return self.driver.page_source


class BrowserPool(LoggerMixin):
    """浏览器池管理器 - Selenium版本"""

    def __init__(self, max_browsers: int = None):
        self.settings = get_settings()
        self.max_browsers = max_browsers or self.settings.max_browsers
        self.drivers: Dict[int, SeleniumDriverWrapper] = {}  # account_id -> driver_wrapper

        # 🔧 修复：改为按账号ID分别加锁，支持并发创建
        self.account_locks: Dict[int, asyncio.Lock] = {}  # account_id -> lock
        self.global_lock = asyncio.Lock()  # 仅用于管理account_locks字典

        # 🔧 新增：端口管理机制，避免端口冲突
        self.used_ports: set = set()  # 已使用的端口
        self.account_ports: Dict[int, int] = {}  # account_id -> port 映射
        self.port_range_start = 9222  # 起始端口
        self.port_range_end = 9322   # 结束端口（支持100个并发）
        self.port_lock = asyncio.Lock()  # 端口分配锁

        # 🔧 新增：Chrome创建锁，避免大规模并发时的文件冲突
        import threading
        self.chrome_creation_semaphore = threading.Semaphore(5)  # 限制同时创建Chrome的数量

        self.anti_detection = AntiDetectionEngine()
        self.user_agent = UserAgent()
        self.protected_processes: Dict[int, set] = {}  # account_id -> set of protected PIDs

        # 🔄 关联互动账号持久化管理
        self.persistent_accounts = set()  # 需要保持WebDriver运行的账号ID（仅关联互动账号）

        # 🔧 修复：增加线程池工作线程数，支持更多并发
        from concurrent.futures import ThreadPoolExecutor
        self.executor = ThreadPoolExecutor(max_workers=10, thread_name_prefix="BrowserManager")
        self.pending_futures = set()  # 跟踪所有待处理的Future
        self._shutdown = False  # 关闭标志

        # 🧹 启动自动清理服务
        self._start_auto_cleanup()

    async def initialize(self):
        """初始化浏览器池 - 优化启动速度"""
        try:
            # 🚀 预热ChromeDriver - 避免每次启动时检查
            await self._preload_chromedriver()

            # 🚀 预创建用户数据目录模板
            await self._prepare_user_data_templates()

            self.logger.info("浏览器池初始化完成")
        except Exception as e:
            self.logger.error(f"浏览器池初始化失败: {e}")
            raise

    async def _preload_chromedriver(self):
        """预加载ChromeDriver，避免启动时延迟"""
        try:
            import concurrent.futures
            loop = asyncio.get_event_loop()

            # 在后台线程中预加载
            def preload():
                try:
                    ChromeDriverManager().install()
                    return True
                except Exception as e:
                    self.logger.warning(f"ChromeDriver预加载失败: {e}")
                    return False

            with concurrent.futures.ThreadPoolExecutor(max_workers=1) as executor:
                future = executor.submit(preload)
                success = await loop.run_in_executor(None, lambda: future.result(timeout=10))

            if success:
                self.logger.info("✅ ChromeDriver预加载成功")
            else:
                self.logger.warning("⚠️ ChromeDriver预加载失败，将在启动时重试")

        except Exception as e:
            self.logger.warning(f"ChromeDriver预加载异常: {e}")

    async def _prepare_user_data_templates(self):
        """预创建用户数据目录模板，加速启动"""
        try:
            base_dir = Path(self.settings.browser_user_data_dir)
            base_dir.mkdir(parents=True, exist_ok=True)

            # 创建模板目录
            template_dir = base_dir / "template"
            if not template_dir.exists():
                template_dir.mkdir(parents=True, exist_ok=True)

                # 创建基本的Chrome配置文件
                prefs_file = template_dir / "Default" / "Preferences"
                prefs_file.parent.mkdir(parents=True, exist_ok=True)

                # 基本的Chrome偏好设置
                basic_prefs = {
                    "profile": {
                        "default_content_setting_values": {
                            "notifications": 2,  # 禁用通知
                            "geolocation": 2,    # 禁用地理位置
                        },
                        "managed_default_content_settings": {
                            "images": 1  # 允许图片（调试时需要）
                        }
                    },
                    "browser": {
                        "check_default_browser": False
                    }
                }

                import json
                with open(prefs_file, 'w', encoding='utf-8') as f:
                    json.dump(basic_prefs, f, indent=2)

                self.logger.info("✅ 用户数据目录模板创建成功")

        except Exception as e:
            self.logger.warning(f"用户数据目录模板创建失败: {e}")

    def mark_account_persistent(self, account_id: int):
        """标记账号为持久化（关联互动账号）"""
        self.persistent_accounts.add(account_id)
        self.logger.info(f"账号 {account_id} 标记为持久化，WebDriver将保持运行")

    def unmark_account_persistent(self, account_id: int):
        """取消账号持久化标记"""
        self.persistent_accounts.discard(account_id)
        self.logger.info(f"账号 {account_id} 取消持久化标记")

    def is_account_persistent(self, account_id: int) -> bool:
        """检查账号是否为持久化账号"""
        return account_id in self.persistent_accounts

    async def _get_account_lock(self, account_id: int) -> asyncio.Lock:
        """获取账号专用锁"""
        async with self.global_lock:
            if account_id not in self.account_locks:
                self.account_locks[account_id] = asyncio.Lock()
            return self.account_locks[account_id]

    def _is_port_available(self, port: int) -> bool:
        """检查端口是否可用"""
        import socket
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(('127.0.0.1', port))
                return True
        except OSError:
            return False

    async def _allocate_port(self, account_id: int) -> int:
        """为账号分配可用端口"""
        async with self.port_lock:
            # 如果账号已有端口，检查是否仍可用
            if account_id in self.account_ports:
                port = self.account_ports[account_id]
                if self._is_port_available(port):
                    self.logger.debug(f"重用端口 {port} (账号 {account_id})")
                    return port
                else:
                    # 端口被占用，需要重新分配
                    self.used_ports.discard(port)
                    del self.account_ports[account_id]
                    self.logger.warning(f"端口 {port} 被占用，重新分配 (账号 {account_id})")

            # 分配新端口
            for port in range(self.port_range_start, self.port_range_end + 1):
                if port not in self.used_ports and self._is_port_available(port):
                    self.used_ports.add(port)
                    self.account_ports[account_id] = port
                    self.logger.info(f"分配端口 {port} (账号 {account_id})")
                    return port

            raise Exception(f"无可用端口 (范围: {self.port_range_start}-{self.port_range_end})")

    async def _release_port(self, account_id: int):
        """释放账号占用的端口"""
        async with self.port_lock:
            if account_id in self.account_ports:
                port = self.account_ports[account_id]
                self.used_ports.discard(port)
                del self.account_ports[account_id]
                self.logger.info(f"释放端口 {port} (账号 {account_id})")

    async def get_driver(self, account: Account) -> Optional[SeleniumDriverWrapper]:
        """
        获取账号对应的WebDriver - 支持并发创建

        Args:
            account: 账号对象

        Returns:
            WebDriver包装器
        """
        # 🔧 修复：使用账号专用锁，允许多个账号并发创建WebDriver
        account_lock = await self._get_account_lock(account.id)

        async with account_lock:
            if account.id in self.drivers:
                # 检查驱动是否仍然有效
                driver_wrapper = self.drivers[account.id]
                self.logger.debug(f"找到现有WebDriver，检查有效性: {account.username}")
                try:
                    if await driver_wrapper.is_session_valid():
                        self.logger.info(f"✅ 重用现有WebDriver: {account.username}")
                        return driver_wrapper
                    else:
                        # 驱动无效，需要重新创建
                        self.logger.info(f"🔄 WebDriver会话无效，重新创建: {account.username}")
                        await self._cleanup_account_driver(account.id)
                        # 释放端口，准备重新分配
                        await self._release_port(account.id)
                except Exception as e:
                    self.logger.info(f"🔄 检查WebDriver会话失败，重新创建: {account.username}, 错误: {e}")
                    await self._cleanup_account_driver(account.id)
                    # 释放端口，准备重新分配
                    await self._release_port(account.id)
            else:
                self.logger.debug(f"未找到现有WebDriver，需要创建新的: {account.username}")

            # 🔧 分配端口
            try:
                debug_port = await self._allocate_port(account.id)
                self.logger.info(f"为账号 {account.username} 分配调试端口: {debug_port}")
            except Exception as e:
                self.logger.error(f"端口分配失败: {e}")
                return None

            # 🚀 智能创建策略 - 支持并发创建
            for attempt in range(3):
                try:
                    self.logger.info(f"创建WebDriver尝试 {attempt + 1}/3: {account.username}")

                    # 🔧 每次尝试前都进行清理
                    if attempt > 0:
                        self.logger.info(f"尝试 {attempt + 1}: 清理Chrome进程...")
                        self._cleanup_chrome_processes_for_account(account.id)
                        await asyncio.sleep(2)  # 等待清理完成

                    # 🚀 优先使用快速创建方法（传入端口）
                    if attempt == 0:
                        driver_wrapper = await self._fast_create_driver(account, debug_port)
                    else:
                        # 备用方法：传统创建（传入端口）
                        driver_wrapper = await self._create_driver(account, debug_port)

                    if driver_wrapper:
                        self.drivers[account.id] = driver_wrapper

                        # 🔒 保护当前账号的Chrome进程
                        await self._protect_chrome_processes(account.id)

                        # 🔄 修复：智能清理机制（跳过持久化账号）
                        async with self.global_lock:
                            if len(self.drivers) > self.max_browsers:
                                # 检查是否有非持久化账号可以清理
                                non_persistent_count = len([
                                    account_id for account_id in self.drivers.keys()
                                    if not self.is_account_persistent(account_id)
                                ])

                                if non_persistent_count > 0:
                                    self.logger.debug(f"清理非持久化WebDriver，当前总数: {len(self.drivers)}")
                                    await self._cleanup_oldest_driver()
                                else:
                                    self.logger.debug(f"所有WebDriver都是持久化账号，跳过清理，当前数量: {len(self.drivers)}")

                        self.logger.info(f"✅ WebDriver创建成功: {account.username}")
                        return driver_wrapper

                except Exception as e:
                    self.logger.warning(f"创建WebDriver失败 (尝试 {attempt + 1}/3): {e}")

                    # 🔧 只在失败时清理，成功时不清理
                    if attempt < 2:  # 不是最后一次尝试
                        self.logger.info(f"尝试 {attempt + 1} 失败，清理后重试...")
                        self._cleanup_chrome_processes_for_account(account.id)
                        await asyncio.sleep(2)  # 增加重试等待时间
                    continue

            # 🔧 所有尝试都失败，释放端口
            await self._release_port(account.id)
            self.logger.error(f"创建WebDriver最终失败: {account.username}")
            return None
    
    async def _create_driver(self, account: Account, debug_port: int = None) -> Optional[SeleniumDriverWrapper]:
        """
        创建WebDriver

        Args:
            account: 账号对象

        Returns:
            WebDriver包装器
        """
        try:
            # 生成浏览器指纹
            fingerprint = self.anti_detection.generate_fingerprint(account.proxy)

            # 🚀 智能用户数据目录选择 - 优先使用最可靠的目录
            user_data_dir = self._get_optimal_user_data_dir(account.id)

            # 配置Chrome选项
            options = uc.ChromeOptions()

            # 基本选项
            options.add_argument(f"--user-data-dir={user_data_dir}")
            options.add_argument(f"--user-agent={fingerprint['userAgent']}")
            options.add_argument(f"--window-size={fingerprint['viewport']['width']},{fingerprint['viewport']['height']}")

            # 配置代理
            if account.proxy:
                proxy_url = account.proxy.proxy_url
                if account.proxy.username and account.proxy.password:
                    # 如果有用户名密码，需要特殊处理
                    proxy_url = f"{account.proxy.username}:{account.proxy.password}@{proxy_url}"
                options.add_argument(f"--proxy-server={proxy_url}")

            # 简化的反检测选项 - 只保留必要的选项
            options.add_argument('--disable-blink-features=AutomationControlled')
            options.add_argument('--disable-dev-shm-usage')
            options.add_argument('--no-first-run')
            options.add_argument('--no-default-browser-check')
            options.add_argument('--disable-infobars')
            options.add_argument('--disable-extensions')

            # 如果是有头模式，移除一些可能导致问题的选项
            if not self.settings.browser_headless:
                self.logger.info("使用有头模式，简化Chrome选项")
            else:
                # 无头模式需要的额外选项
                options.add_argument('--disable-gpu')
                options.add_argument('--no-sandbox')
            options.add_argument('--disable-features=TranslateUI')
            options.add_argument('--disable-hang-monitor')
            options.add_argument('--disable-prompt-on-repost')
            options.add_argument('--disable-background-mode')
            options.add_argument('--force-color-profile=srgb')
            options.add_argument('--metrics-recording-only')
            options.add_argument('--safebrowsing-disable-auto-update')
            options.add_argument('--enable-automation=false')
            options.add_argument('--password-store=basic')
            options.add_argument('--use-mock-keychain')

            # 设置headless模式
            if self.settings.browser_headless:
                options.add_argument('--headless=new')

            # 设置语言和时区
            options.add_argument(f"--lang={fingerprint.get('language', 'en-US')}")

            # 禁用图片加载以提高速度（可选）
            # options.add_argument('--blink-settings=imagesEnabled=false')

            # 简化实验性选项配置
            try:
                # 设置prefs - 只设置基本的偏好设置
                prefs = {
                    "profile.default_content_setting_values": {
                        "notifications": 2,  # 禁用通知
                    }
                }
                options.add_experimental_option("prefs", prefs)
            except Exception as e:
                self.logger.warning(f"设置实验性选项失败，跳过: {e}")
            
            # 🚀 优化的Chrome创建函数
            def create_driver():
                try:
                    import time
                    start_time = time.time()

                    # 🚀 快速创建Chrome选项
                    options = self._create_optimized_chrome_options(account, fingerprint, user_data_dir)

                    self.logger.info("🚀 创建Chrome实例...")

                    # 🚀 优化的Chrome实例创建 - 支持大规模并发
                    with self.chrome_creation_semaphore:  # 🔧 限制并发Chrome创建
                        self.logger.info(f"获得Chrome创建许可，开始创建: {account.username}")

                        # 🔧 修复：传递端口参数给undetected-chromedriver
                        uc_kwargs = {
                            'options': options,
                            'version_main': None,
                            'suppress_welcome': True,
                            'use_subprocess': False,
                            'debug': False,
                            'service_args': ['--silent', '--log-level=3', '--disable-logging']  # 减少日志输出
                        }

                        # 注意：这个方法没有debug_port参数，所以不传递端口
                        # 端口已经通过options传递了

                        driver = uc.Chrome(**uc_kwargs)

                    end_time = time.time()
                    startup_time = end_time - start_time
                    self.logger.info(f"✅ Chrome创建成功！耗时: {startup_time:.2f}秒")

                    return driver

                except Exception as e:
                    self.logger.error(f"Chrome创建失败: {e}")
                    raise

            # 🔧 修复：确保在正确的事件循环中执行
            if self._shutdown:
                raise Exception("浏览器池已关闭，取消Chrome启动")

            # 🔧 修复：获取当前事件循环，避免跨线程事件循环冲突
            try:
                loop = asyncio.get_running_loop()
            except RuntimeError:
                # 如果没有运行中的事件循环，创建一个新的
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)

            future = loop.run_in_executor(self.executor, create_driver)
            self.pending_futures.add(future)

            try:
                driver = await future
            finally:
                self.pending_futures.discard(future)

            # 创建包装器
            driver_wrapper = SeleniumDriverWrapper(driver, account.id)

            # 注入反检测脚本
            await self._inject_stealth_scripts(driver_wrapper)

            # 设置cookies
            if account.cookies:
                await self._set_cookies(driver_wrapper, account.cookies)

            # 保存指纹信息
            from src.core.account_manager import AccountManager
            account_manager = AccountManager()
            account_manager.update_account_fingerprint(account.id, fingerprint)

            self.logger.info(f"创建WebDriver成功: {account.username}")
            return driver_wrapper

        except Exception as e:
            self.logger.error(f"创建WebDriver失败: {e}")
            return None

    def _create_optimized_chrome_options(self, account: Account, fingerprint: dict, user_data_dir: Path, debug_port: int = None):
        """🚀 创建优化的Chrome选项 - 解决连接和兼容性问题"""
        options = uc.ChromeOptions()

        # 🎯 核心必需选项
        options.add_argument(f"--user-data-dir={user_data_dir}")
        options.add_argument(f"--user-agent={fingerprint['userAgent']}")
        options.add_argument(f"--window-size={fingerprint['viewport']['width']},{fingerprint['viewport']['height']}")

        # 🔧 关键：设置调试端口，避免端口冲突
        if debug_port:
            options.add_argument(f"--remote-debugging-port={debug_port}")

        # 🌐 代理设置
        if account.proxy:
            proxy_url = account.proxy.proxy_url
            if account.proxy.username and account.proxy.password:
                proxy_url = f"{account.proxy.username}:{account.proxy.password}@{proxy_url}"
            options.add_argument(f"--proxy-server={proxy_url}")

        # 🔧 连接稳定性参数 - 解决Chrome连接问题
        stability_args = [
            '--no-sandbox',
            '--disable-dev-shm-usage',
            '--disable-gpu-sandbox',
            '--disable-software-rasterizer',
            '--disable-background-timer-throttling',
            '--disable-renderer-backgrounding',
            '--disable-backgrounding-occluded-windows',
            '--disable-features=VizDisplayCompositor',
            '--disable-features=TranslateUI',
            '--disable-ipc-flooding-protection',
            '--disable-hang-monitor',
            '--disable-prompt-on-repost',
            '--disable-domain-reliability',
            '--disable-component-extensions-with-background-pages',
            '--disable-background-networking',
            '--disable-sync',
            '--disable-default-apps',
            '--disable-extensions',
            '--disable-plugins-discovery',
            '--disable-translate',
            '--no-first-run',
            '--no-default-browser-check',
            '--disable-infobars',
            '--disable-web-security',
            '--disable-features=VizDisplayCompositor',
            '--disable-logging',
            '--silent'
        ]

        # 🛡️ 反检测参数
        anti_detection_args = [
            '--disable-blink-features=AutomationControlled',
            '--exclude-switches=enable-automation',
            '--disable-extensions-file-access-check',
            '--disable-extensions-http-throttling'
        ]

        # 🚀 性能优化参数
        if self.settings.browser_headless:
            performance_args = [
                '--headless=new',
                '--disable-gpu',
                '--disable-images',
                '--disable-plugins',
                '--disable-java',
                '--disable-flash'
            ]
        else:
            performance_args = [
                '--disable-plugins',
                '--disable-java',
                '--disable-flash'
            ]

        # 🔧 批量添加参数 - 避免重复
        all_args = stability_args + anti_detection_args + performance_args
        unique_args = list(dict.fromkeys(all_args))  # 去重

        for arg in unique_args:
            try:
                options.add_argument(arg)
            except Exception as e:
                self.logger.debug(f"添加Chrome参数失败: {arg}, 错误: {e}")
                continue

        # 🎛️ 性能偏好设置
        prefs = {
            "profile.default_content_setting_values": {
                "notifications": 2,  # 禁用通知
                "geolocation": 2,    # 禁用地理位置
                "media_stream": 2,   # 禁用摄像头/麦克风
            },
            "profile.managed_default_content_settings": {
                "images": 1 if not self.settings.browser_headless else 2  # 有头模式保留图片
            }
        }
        options.add_experimental_option("prefs", prefs)

        # 🔧 反检测已在essential_args中包含，无需额外设置

        return options

    async def _fast_create_driver(self, account: Account, debug_port: int = None) -> Optional[SeleniumDriverWrapper]:
        """🚀 快速创建WebDriver - 解决Chrome连接问题"""
        try:
            import time
            total_start_time = time.time()

            # 🔧 步骤0: 预清理可能的Chrome进程冲突
            await self._pre_cleanup_chrome_conflicts(account.id)

            # 🚀 步骤1: 快速生成指纹（缓存优化）
            fingerprint = self.anti_detection.generate_fingerprint(account.proxy)

            # 🚀 步骤2: 快速获取用户数据目录
            user_data_dir = self._get_optimal_user_data_dir(account.id)

            # 🚀 步骤3: 预创建Chrome选项（包含端口）
            options = self._create_optimized_chrome_options(account, fingerprint, user_data_dir, debug_port)

            # 🚀 步骤4: 彻底重构Chrome创建 - 解决所有连接问题
            def create_chrome_with_complete_cleanup():
                # 🔧 修复：检查是否已关闭
                if self._shutdown:
                    raise Exception("浏览器池已关闭，取消Chrome启动")

                max_attempts = 3
                for attempt in range(max_attempts):
                    try:
                        # 🔧 修复：每次尝试前检查关闭标志
                        if self._shutdown:
                            raise Exception("浏览器池已关闭，取消Chrome启动")

                        self.logger.info(f"🚀 Chrome启动尝试 {attempt + 1}/{max_attempts}...")
                        start = time.time()

                        # 🔧 每次尝试前彻底清理
                        if attempt > 0:
                            # 🔧 修复：清理前再次检查关闭标志
                            if self._shutdown:
                                raise Exception("浏览器池已关闭，取消Chrome启动")

                            self.logger.info(f"尝试 {attempt + 1}: 彻底清理Chrome环境...")
                            self._complete_chrome_cleanup(account.id)
                            time.sleep(3)  # 增加等待时间

                            # 🔧 修复：清理后再次检查关闭标志
                            if self._shutdown:
                                raise Exception("浏览器池已关闭，取消Chrome启动")

                        # 🔧 重新创建Chrome选项 - 解决重用问题（包含端口）
                        fresh_options = self._create_fresh_chrome_options(account, fingerprint, user_data_dir, debug_port)

                        # � 修复：Chrome创建前再次检查关闭标志
                        if self._shutdown:
                            raise Exception("浏览器池已关闭，取消Chrome启动")

                        # �🚀 使用最简化的Chrome创建方式 - 项目独立目录
                        try:
                            # 🔧 限制并发Chrome创建，避免文件冲突
                            with self.chrome_creation_semaphore:
                                self.logger.info(f"获得Chrome创建许可（备用方案），开始创建: {account.username}")

                                # 🔧 修复：传递端口参数给undetected-chromedriver
                                uc_kwargs = {
                                    'options': fresh_options,
                                    'version_main': None,
                                    'suppress_welcome': True,
                                    'use_subprocess': True,  # 改为True，提高稳定性
                                    'debug': False
                                }

                                # 如果有调试端口，传递给undetected-chromedriver
                                if debug_port:
                                    uc_kwargs['port'] = debug_port
                                    self.logger.debug(f"传递调试端口给undetected-chromedriver: {debug_port}")

                                driver = uc.Chrome(**uc_kwargs)
                        except Exception as chrome_error:
                            self.logger.warning(f"undetected-chromedriver失败，尝试标准Selenium: {chrome_error}")

                            # 🔧 诊断信息：记录端口分配情况
                            self.logger.info(f"诊断信息 - 分配端口: {debug_port}, 账号: {account.username}")

                            # 🔧 检查端口是否真的可用
                            if debug_port and not self._is_port_available(debug_port):
                                self.logger.warning(f"分配的端口 {debug_port} 实际不可用，可能导致连接失败")

                            # 🔧 修复：备用方案前检查关闭标志
                            if self._shutdown:
                                raise Exception("浏览器池已关闭，取消Chrome启动")

                            # 🔧 新增：检查是否为目录损坏问题，尝试自动修复
                            if self._is_connection_error(chrome_error) and self._can_auto_repair(account.id):
                                self.logger.info(f"🔍 检测到连接失败，尝试自动修复: {account.username}")
                                if self._auto_fix_corrupted_profile(account, user_data_dir):
                                    self.logger.info(f"🎉 自动修复成功，重新创建Chrome: {account.username}")
                                    # 重新创建Chrome选项（使用修复后的干净目录）
                                    fresh_options = self._create_fresh_chrome_options(account, fingerprint, user_data_dir, debug_port)

                                    # 重新尝试undetected-chromedriver
                                    uc_kwargs['options'] = fresh_options
                                    try:
                                        driver = uc.Chrome(**uc_kwargs)
                                        self.logger.info(f"✅ 自动修复后Chrome创建成功: {account.username}")
                                        return driver
                                    except Exception as retry_error:
                                        self.logger.warning(f"自动修复后仍失败，使用标准Selenium: {retry_error}")

                            # 备用方案：使用标准Selenium
                            from selenium import webdriver
                            from selenium.webdriver.chrome.service import Service

                            service = Service()
                            self.logger.info(f"使用标准Selenium创建Chrome，端口: {debug_port}")
                            driver = webdriver.Chrome(service=service, options=fresh_options)

                        end = time.time()
                        self.logger.info(f"✅ Chrome启动成功！耗时: {end - start:.2f}秒")
                        return driver

                    except Exception as e:
                        self.logger.warning(f"Chrome启动尝试 {attempt + 1} 失败: {e}")
                        if attempt < max_attempts - 1:
                            # 彻底清理后重试
                            self._complete_chrome_cleanup(account.id)
                            time.sleep(5)  # 增加等待时间
                        else:
                            # 最后一次尝试失败，记录详细错误
                            self.logger.error(f"Chrome启动彻底失败: {e}")
                            raise

                raise Exception("所有Chrome启动尝试都失败")

            # 🔧 修复：确保在正确的事件循环中执行
            if self._shutdown:
                raise Exception("浏览器池已关闭，取消Chrome启动")

            # 🔧 修复：获取当前事件循环，避免跨线程事件循环冲突
            try:
                loop = asyncio.get_running_loop()
            except RuntimeError:
                # 如果没有运行中的事件循环，创建一个新的
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)

            future = loop.run_in_executor(self.executor, create_chrome_with_complete_cleanup)
            self.pending_futures.add(future)

            try:
                driver = await future
            finally:
                self.pending_futures.discard(future)

            # 🚀 步骤5: 创建包装器
            driver_wrapper = SeleniumDriverWrapper(driver, account.id)

            # 🚀 步骤6: 同步注入脚本（避免会话断开）
            try:
                await self._inject_stealth_scripts(driver_wrapper)
                self.logger.debug("反检测脚本注入成功")
            except Exception as e:
                self.logger.warning(f"注入反检测脚本失败: {e}")

            # 🚀 步骤7: 同步设置cookies（避免会话断开）
            if account.cookies:
                try:
                    await self._set_cookies(driver_wrapper, account.cookies)
                    self.logger.debug("Cookies设置成功")
                except Exception as e:
                    self.logger.warning(f"设置cookies失败: {e}")

            # 🔧 等待Driver完全稳定
            await asyncio.sleep(1)

            # 🔧 验证Driver会话稳定性
            try:
                is_valid = await driver_wrapper.is_session_valid()
                if not is_valid:
                    raise Exception("Driver会话创建后立即失效")
                self.logger.debug("Driver会话稳定性验证通过")
            except Exception as e:
                self.logger.warning(f"Driver会话稳定性验证失败: {e}")
                # 不抛出异常，继续使用

            total_end_time = time.time()
            total_time = total_end_time - total_start_time
            self.logger.info(f"🎉 WebDriver快速创建完成！总耗时: {total_time:.2f}秒")

            return driver_wrapper

        except Exception as e:
            self.logger.error(f"快速创建WebDriver失败: {e}")
            # 最后清理
            self._cleanup_chrome_processes_for_account(account.id)
            return None

    async def _pre_cleanup_chrome_conflicts(self, account_id: int):
        """保守的预清理Chrome进程冲突 - 只清理明确有问题的进程"""
        try:
            self.logger.debug(f"保守预清理Chrome冲突 (账号 {account_id})...")

            # 只清理端口冲突，不清理正常的Chrome进程
            self._cleanup_port_conflicts()

            # 只清理明确崩溃或僵死的Chrome进程
            self._cleanup_dead_chrome_processes(account_id)

            # 等待清理完成
            await asyncio.sleep(0.3)

        except Exception as e:
            self.logger.debug(f"预清理Chrome冲突失败: {e}")

    def _cleanup_chrome_processes_for_account(self, account_id: int):
        """清理特定账号的Chrome进程"""
        try:
            import psutil
            import subprocess

            cleaned_count = 0
            user_data_pattern = f"account_{account_id}"

            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    if proc.info['name'] and 'chrome' in proc.info['name'].lower():
                        cmdline = proc.info['cmdline'] or []
                        cmdline_str = ' '.join(cmdline)

                        # 检查是否是该账号的Chrome进程
                        if user_data_pattern in cmdline_str:
                            # 🔒 检查进程是否被保护
                            if self._is_process_protected(account_id, proc.info['pid']):
                                self.logger.debug(f"跳过保护的Chrome进程: {proc.info['pid']}")
                                continue

                            self.logger.debug(f"终止账号 {account_id} 的Chrome进程: {proc.info['pid']}")
                            proc.terminate()
                            cleaned_count += 1

                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
                except Exception as e:
                    self.logger.debug(f"清理进程失败: {e}")
                    continue

            if cleaned_count > 0:
                self.logger.info(f"清理了 {cleaned_count} 个账号 {account_id} 的Chrome进程")

        except Exception as e:
            self.logger.debug(f"清理账号Chrome进程失败: {e}")

    def _cleanup_port_conflicts(self):
        """清理端口冲突"""
        try:
            import psutil

            # 检查常用的Chrome调试端口
            common_ports = [9222, 9223, 9224, 9225, 9226]

            for port in common_ports:
                try:
                    for conn in psutil.net_connections():
                        if conn.laddr.port == port and conn.status == 'LISTEN':
                            try:
                                proc = psutil.Process(conn.pid)
                                if 'chrome' in proc.name().lower():
                                    self.logger.debug(f"终止占用端口 {port} 的Chrome进程: {conn.pid}")
                                    proc.terminate()
                            except (psutil.NoSuchProcess, psutil.AccessDenied):
                                continue
                except Exception:
                    continue

        except Exception as e:
            self.logger.debug(f"清理端口冲突失败: {e}")

    def _cleanup_dead_chrome_processes(self, account_id: int):
        """只清理明确崩溃或僵死的Chrome进程"""
        try:
            import psutil

            cleaned_count = 0
            user_data_pattern = f"account_{account_id}"

            for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'status']):
                try:
                    if proc.info['name'] and 'chrome' in proc.info['name'].lower():
                        cmdline = proc.info['cmdline'] or []
                        cmdline_str = ' '.join(cmdline)

                        # 只处理该账号的Chrome进程
                        if user_data_pattern in cmdline_str:
                            # 检查进程状态
                            status = proc.info.get('status', '')

                            # 只清理明确有问题的进程
                            if status in ['zombie', 'stopped'] or not proc.is_running():
                                self.logger.debug(f"清理僵死Chrome进程: {proc.info['pid']} (状态: {status})")
                                proc.terminate()
                                cleaned_count += 1
                            else:
                                # 检查进程是否响应
                                try:
                                    # 尝试获取进程信息，如果失败说明进程有问题
                                    proc.memory_info()
                                except (psutil.NoSuchProcess, psutil.AccessDenied):
                                    self.logger.debug(f"清理无响应Chrome进程: {proc.info['pid']}")
                                    proc.terminate()
                                    cleaned_count += 1

                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
                except Exception as e:
                    self.logger.debug(f"检查进程状态失败: {e}")
                    continue

            if cleaned_count > 0:
                self.logger.info(f"清理了 {cleaned_count} 个有问题的账号 {account_id} Chrome进程")

        except Exception as e:
            self.logger.debug(f"清理僵死Chrome进程失败: {e}")

    async def _protect_chrome_processes(self, account_id: int):
        """保护当前账号的Chrome进程，避免被误清理"""
        try:
            import psutil

            protected_pids = set()
            user_data_pattern = f"account_{account_id}"

            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    if proc.info['name'] and 'chrome' in proc.info['name'].lower():
                        cmdline = proc.info['cmdline'] or []
                        cmdline_str = ' '.join(cmdline)

                        # 保护该账号的Chrome进程
                        if user_data_pattern in cmdline_str:
                            protected_pids.add(proc.info['pid'])

                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
                except Exception:
                    continue

            self.protected_processes[account_id] = protected_pids
            if protected_pids:
                self.logger.debug(f"保护账号 {account_id} 的 {len(protected_pids)} 个Chrome进程")

        except Exception as e:
            self.logger.debug(f"保护Chrome进程失败: {e}")

    def _is_process_protected(self, account_id: int, pid: int) -> bool:
        """检查进程是否被保护"""
        return account_id in self.protected_processes and pid in self.protected_processes[account_id]

    def _complete_chrome_cleanup(self, account_id: int):
        """彻底清理Chrome环境 - 解决所有连接问题"""
        try:
            import subprocess
            import psutil
            import time

            self.logger.info(f"开始彻底清理Chrome环境 (账号 {account_id})...")

            # 步骤1: 清理特定账号的Chrome进程
            self._cleanup_chrome_processes_for_account(account_id)

            # 步骤2: 精确清理自动化Chrome进程
            automation_count = 0
            user_count = 0
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    if proc.info['name'] and 'chrome' in proc.info['name'].lower():
                        # 🔧 使用方案2判断是否为自动化Chrome
                        if self._is_automation_chrome(proc):
                            self.logger.debug(f"清理自动化Chrome进程: {proc.info['pid']}")
                            proc.terminate()
                            automation_count += 1
                        else:
                            self.logger.debug(f"跳过用户Chrome进程: {proc.info['pid']}")
                            user_count += 1

                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
                except Exception:
                    continue

            if automation_count > 0:
                self.logger.info(f"清理了 {automation_count} 个自动化Chrome进程，保护了 {user_count} 个用户Chrome进程")

            # 步骤3: 清理端口冲突
            self._cleanup_port_conflicts()

            # 步骤4: 清理ChromeDriver进程
            try:
                for proc in psutil.process_iter(['pid', 'name']):
                    if proc.info['name'] and 'chromedriver' in proc.info['name'].lower():
                        self.logger.debug(f"清理ChromeDriver进程: {proc.info['pid']}")
                        proc.terminate()
            except Exception as e:
                self.logger.debug(f"清理ChromeDriver进程失败: {e}")

            # 步骤5: 等待进程完全终止
            time.sleep(2)

            self.logger.info("Chrome环境彻底清理完成")

        except Exception as e:
            self.logger.error(f"彻底清理Chrome环境失败: {e}")

    def _create_fresh_chrome_options(self, account: Account, fingerprint: dict, user_data_dir: Path, debug_port: int = None):
        """创建全新的Chrome选项 - 解决重用问题"""
        try:
            # 每次都创建全新的选项对象
            options = uc.ChromeOptions()

            # 🎯 核心必需选项
            options.add_argument(f"--user-data-dir={user_data_dir}")
            options.add_argument(f"--user-agent={fingerprint['userAgent']}")
            options.add_argument(f"--window-size={fingerprint['viewport']['width']},{fingerprint['viewport']['height']}")

            # 🔧 关键：设置调试端口，避免端口冲突
            if debug_port:
                options.add_argument(f"--remote-debugging-port={debug_port}")

            # 🌐 代理设置
            if account.proxy:
                proxy_url = account.proxy.proxy_url
                if account.proxy.username and account.proxy.password:
                    proxy_url = f"{account.proxy.username}:{account.proxy.password}@{proxy_url}"
                options.add_argument(f"--proxy-server={proxy_url}")

            # 🔧 最小化但稳定的参数集
            essential_args = [
                '--no-sandbox',
                '--disable-dev-shm-usage',
                '--disable-blink-features=AutomationControlled',
                '--no-first-run',
                '--no-default-browser-check',
                '--disable-infobars',

                # 🌐 网络连接优化
                '--ignore-ssl-errors',
                '--ignore-certificate-errors',
                '--ignore-certificate-errors-spki-list',
                '--ignore-certificate-errors-skip-list',
                '--disable-ssl-false-start',
                '--disable-tls13-early-data',
                '--aggressive-cache-discard',
                '--disable-background-networking',
                '--disable-background-timer-throttling',
                '--disable-async-dns',
                '--disable-component-update',
                '--disable-sync',
                '--disable-background-downloads',
                '--disable-extensions',
                '--disable-default-apps',
                '--disable-sync',
                '--disable-translate',
                '--disable-logging',
                '--silent',
                '--disable-gpu-sandbox'
            ]

            # 🚀 性能优化参数
            if self.settings.browser_headless:
                essential_args.extend([
                    '--headless=new',
                    '--disable-gpu'
                ])

            # 批量添加参数
            for arg in essential_args:
                try:
                    options.add_argument(arg)
                except Exception as e:
                    self.logger.debug(f"添加Chrome参数失败: {arg}, 错误: {e}")
                    continue

            # 🎛️ 基础偏好设置
            prefs = {
                "profile.default_content_setting_values": {
                    "notifications": 2,
                    "geolocation": 2,
                    "media_stream": 2,
                }
            }
            options.add_experimental_option("prefs", prefs)

            return options

        except Exception as e:
            self.logger.error(f"创建Chrome选项失败: {e}")
            # 返回最基础的选项
            basic_options = uc.ChromeOptions()
            basic_options.add_argument(f"--user-data-dir={user_data_dir}")
            basic_options.add_argument('--no-sandbox')
            basic_options.add_argument('--disable-dev-shm-usage')
            return basic_options

    def _smart_cleanup_chrome_processes(self, account_id: int):
        """智能清理Chrome进程 - 只清理可能冲突的进程"""
        try:
            import subprocess
            import psutil
            import time

            # 检查是否有Chrome进程占用相关端口
            chrome_processes = []
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    if proc.info['name'] and 'chrome' in proc.info['name'].lower():
                        cmdline = proc.info['cmdline'] or []
                        cmdline_str = ' '.join(cmdline)

                        # 检查是否是自动化相关的Chrome进程
                        if any(keyword in cmdline_str for keyword in [
                            '--remote-debugging-port',
                            '--user-data-dir',
                            'chromedriver',
                            f'account_{account_id}'
                        ]):
                            chrome_processes.append(proc.info['pid'])
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue

            if chrome_processes:
                self.logger.info(f"发现 {len(chrome_processes)} 个可能冲突的Chrome进程，正在清理...")

                # 温和地终止进程
                for pid in chrome_processes:
                    try:
                        proc = psutil.Process(pid)
                        proc.terminate()  # 发送SIGTERM
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        continue

                # 等待进程退出
                time.sleep(2)

                # 强制杀死仍然存在的进程
                for pid in chrome_processes:
                    try:
                        proc = psutil.Process(pid)
                        if proc.is_running():
                            proc.kill()  # 强制杀死
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        continue

                self.logger.info("Chrome进程清理完成")
            else:
                self.logger.debug("未发现冲突的Chrome进程")

        except ImportError:
            # 如果psutil不可用，使用简单的清理方式
            self.logger.debug("psutil不可用，使用简单清理方式")
            try:
                import subprocess
                subprocess.run(['taskkill', '/f', '/im', 'chromedriver.exe'],
                             capture_output=True, check=False, timeout=5)
            except Exception:
                pass
        except Exception as e:
            self.logger.debug(f"智能清理失败，继续启动: {e}")

    def _get_optimal_user_data_dir(self, account_id: int) -> Path:
        """获取最优的用户数据目录 - 优先使用项目目录"""
        import tempfile
        import os
        import stat

        # 获取程序根目录
        program_root = Path(__file__).parent.parent.parent

        # 🎯 优先使用项目目录（便携性和性能最佳）
        project_data_dir = program_root / "data" / "browser_profiles" / f"account_{account_id}"

        try:
            # 创建项目数据目录
            project_data_dir.mkdir(parents=True, exist_ok=True)

            # 测试写入权限
            test_file = project_data_dir / "permission_test.tmp"
            test_file.write_text("test")
            test_file.unlink()  # 删除测试文件

            # 设置目录权限（如果可能）
            try:
                os.chmod(project_data_dir, stat.S_IRWXU | stat.S_IRWXG | stat.S_IRWXO)
            except Exception:
                pass  # 权限设置失败不影响使用

            self.logger.info(f"✅ 使用项目数据目录: {project_data_dir}")
            return project_data_dir

        except Exception as e:
            self.logger.warning(f"⚠️ 项目目录不可用: {e}")

            # 备选方案1: 项目临时目录
            try:
                project_temp_dir = program_root / "temp" / "chrome_profiles" / f"account_{account_id}"
                project_temp_dir.mkdir(parents=True, exist_ok=True)

                # 测试写入权限
                test_file = project_temp_dir / "permission_test.tmp"
                test_file.write_text("test")
                test_file.unlink()

                self.logger.info(f"✅ 使用项目临时目录: {project_temp_dir}")
                return project_temp_dir

            except Exception as e2:
                self.logger.warning(f"⚠️ 项目临时目录不可用: {e2}")

                # 最后备选方案: 系统临时目录
                temp_dir = Path(tempfile.mkdtemp(prefix=f"chrome_account_{account_id}_"))
                self.logger.warning(f"🔄 使用系统临时目录: {temp_dir}")
                return temp_dir

    async def _inject_stealth_scripts(self, driver_wrapper: SeleniumDriverWrapper):
        """
        注入反检测脚本

        Args:
            driver_wrapper: WebDriver包装器
        """
        try:
            # 注入增强反检测脚本
            stealth_script = """
            // 隐藏webdriver属性
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined,
            });

            // 删除webdriver相关属性
            delete navigator.__proto__.webdriver;

            // 修改plugins
            Object.defineProperty(navigator, 'plugins', {
                get: () => [
                    {
                        0: {type: "application/x-google-chrome-pdf", suffixes: "pdf", description: "Portable Document Format"},
                        description: "Portable Document Format",
                        filename: "internal-pdf-viewer",
                        length: 1,
                        name: "Chrome PDF Plugin"
                    },
                    {
                        0: {type: "application/pdf", suffixes: "pdf", description: "Portable Document Format"},
                        description: "Portable Document Format",
                        filename: "mhjfbmdgcfjbbpaeojofohoefgiehjai",
                        length: 1,
                        name: "Chrome PDF Viewer"
                    }
                ],
            });

            // 修改languages
            Object.defineProperty(navigator, 'languages', {
                get: () => ['en-US', 'en'],
            });

            // 隐藏自动化相关属性
            window.chrome = {
                runtime: {},
                loadTimes: function() {},
                csi: function() {},
                app: {}
            };

            // 修改权限查询
            const originalQuery = window.navigator.permissions.query;
            window.navigator.permissions.query = (parameters) => (
                parameters.name === 'notifications' ?
                    Promise.resolve({ state: Notification.permission }) :
                    originalQuery(parameters)
            );

            // 隐藏自动化检测
            Object.defineProperty(window, 'outerHeight', {
                get: function() {
                    return window.innerHeight;
                }
            });

            Object.defineProperty(window, 'outerWidth', {
                get: function() {
                    return window.innerWidth;
                }
            });

            // 修改User-Agent相关检测
            Object.defineProperty(navigator, 'platform', {
                get: () => 'Win32',
            });

            // 隐藏Selenium特征
            delete window.cdc_adoQpoasnfa76pfcZLmcfl_Array;
            delete window.cdc_adoQpoasnfa76pfcZLmcfl_Promise;
            delete window.cdc_adoQpoasnfa76pfcZLmcfl_Symbol;

            // 修改toString方法
            window.navigator.chrome = window.chrome;
            window.navigator.chrome.runtime = {
                onConnect: undefined,
                onMessage: undefined,
                sendMessage: undefined,
            };

            // 伪造battery API
            Object.defineProperty(navigator, 'getBattery', {
                get: () => () => Promise.resolve({
                    charging: true,
                    chargingTime: 0,
                    dischargingTime: Infinity,
                    level: Math.random()
                }),
            });

            // 修改iframe检测
            Object.defineProperty(HTMLIFrameElement.prototype, 'contentWindow', {
                get: function() {
                    return window;
                }
            });

            // 隐藏headless特征
            Object.defineProperty(navigator, 'hardwareConcurrency', {
                get: () => 4,
            });

            Object.defineProperty(navigator, 'deviceMemory', {
                get: () => 8,
            });

            // 修改Date对象以避免时区检测
            const originalDate = Date;
            Date = class extends originalDate {
                constructor(...args) {
                    if (args.length === 0) {
                        super();
                    } else {
                        super(...args);
                    }
                }

                getTimezoneOffset() {
                    return 300; // EST timezone
                }
            };

            Date.now = originalDate.now;
            Date.parse = originalDate.parse;
            Date.UTC = originalDate.UTC;

            // 修改screen对象
            Object.defineProperty(screen, 'availHeight', {
                get: () => screen.height,
            });

            Object.defineProperty(screen, 'availWidth', {
                get: () => screen.width,
            });

            // 隐藏Selenium自动化特征
            Object.defineProperty(document, '$cdc_asdjflasutopfhvcZLmcfl_', {
                get: () => undefined,
            });

            Object.defineProperty(document, '$chrome_asyncScriptInfo', {
                get: () => undefined,
            });
            """

            # 执行反检测脚本
            await driver_wrapper.execute_script(stealth_script)

        except Exception as e:
            self.logger.warning(f"注入反检测脚本失败: {e}")

    async def _set_cookies(self, driver_wrapper: SeleniumDriverWrapper, cookies_data: str):
        """
        设置cookies

        Args:
            driver_wrapper: WebDriver包装器
            cookies_data: cookies数据
        """
        try:
            cookies = json.loads(cookies_data)
            if isinstance(cookies, list) and cookies:
                # 先访问域名以设置cookies
                await driver_wrapper.get("https://x.com")

                for cookie in cookies:
                    if isinstance(cookie, dict) and 'name' in cookie and 'value' in cookie:
                        cookie_dict = {
                            'name': cookie['name'],
                            'value': cookie['value'],
                            'domain': cookie.get('domain', '.x.com'),
                            'path': cookie.get('path', '/'),
                        }
                        if 'expiry' in cookie:
                            cookie_dict['expiry'] = cookie['expiry']

                        await driver_wrapper.execute_async(
                            driver_wrapper.driver.add_cookie, cookie_dict
                        )

                self.logger.debug("已设置cookies")

        except Exception as e:
            self.logger.warning(f"设置cookies失败: {e}")
    
    async def _cleanup_oldest_driver(self):
        """清理最旧的WebDriver（跳过持久化账号）"""
        if not self.drivers:
            return

        # 🔄 修复：跳过持久化账号，只清理非持久化账号
        non_persistent_accounts = [
            account_id for account_id in self.drivers.keys()
            if not self.is_account_persistent(account_id)
        ]

        if not non_persistent_accounts:
            self.logger.debug("所有WebDriver都是持久化账号，跳过清理")
            return

        # 清理第一个非持久化账号
        oldest_account_id = non_persistent_accounts[0]
        self.logger.info(f"清理非持久化账号的WebDriver: {oldest_account_id}")
        await self._cleanup_account_driver(oldest_account_id)

    async def _cleanup_account_driver(self, account_id: int):
        """
        清理指定账号的WebDriver

        Args:
            account_id: 账号ID
        """
        try:
            if account_id in self.drivers:
                driver_wrapper = self.drivers[account_id]

                # 安全关闭WebDriver
                try:
                    # 先检查WebDriver是否仍然有效
                    if hasattr(driver_wrapper, 'driver') and driver_wrapper.driver:
                        try:
                            # 检查会话是否有效
                            is_valid = await driver_wrapper.is_session_valid()
                            if is_valid:
                                await driver_wrapper.close()
                                self.logger.debug(f"WebDriver正常关闭: {account_id}")
                            else:
                                self.logger.debug(f"WebDriver会话已无效，跳过关闭: {account_id}")
                        except Exception as e:
                            # 如果是"已关闭"相关的错误，不记录为警告
                            error_msg = str(e).lower()
                            if "driver已关闭" in error_msg or "session" in error_msg or "invalid session" in error_msg:
                                self.logger.debug(f"WebDriver已经关闭: {account_id}")
                            else:
                                self.logger.debug(f"WebDriver关闭异常: {e}")
                    else:
                        self.logger.debug(f"WebDriver对象无效，跳过关闭: {account_id}")

                except Exception as e:
                    # 关闭失败也不是致命错误，继续清理状态
                    error_msg = str(e).lower()
                    if "driver已关闭" in error_msg or "session" in error_msg:
                        self.logger.debug(f"WebDriver已经关闭，继续清理状态: {account_id}")
                    else:
                        self.logger.debug(f"WebDriver关闭失败，继续清理状态: {e}")

                # 无论WebDriver关闭是否成功，都要清理状态
                del self.drivers[account_id]

            # 🔒 清除进程保护
            if account_id in self.protected_processes:
                del self.protected_processes[account_id]

            # 🔧 释放端口
            await self._release_port(account_id)

            self.logger.debug(f"清理账号WebDriver完成: {account_id}")

        except Exception as e:
            # 只有在非预期错误时才记录警告
            error_msg = str(e).lower()
            if "driver已关闭" in error_msg or "session" in error_msg:
                self.logger.debug(f"清理账号WebDriver时发现已关闭: {account_id}")
            else:
                self.logger.warning(f"清理账号WebDriver失败: {e}")
    
    async def close_all(self, force_cleanup=False):
        """关闭所有WebDriver"""
        try:
            self.logger.info(f"开始关闭 {len(self.drivers)} 个WebDriver实例")

            # 并发关闭所有WebDriver
            if self.drivers:
                driver_tasks = []
                account_ids = list(self.drivers.keys())  # 保存账号ID列表

                for account_id, driver_wrapper in self.drivers.items():
                    async def close_driver(dw, aid):
                        try:
                            # 检查WebDriver是否仍然有效
                            if await dw.is_session_valid():
                                await asyncio.wait_for(dw.close(), timeout=10.0)
                                self.logger.debug(f"WebDriver {aid} 已关闭")
                            else:
                                self.logger.debug(f"WebDriver {aid} 会话已无效，跳过关闭")
                        except asyncio.TimeoutError:
                            self.logger.warning(f"WebDriver {aid} 关闭超时")
                        except Exception as e:
                            # 如果是会话已关闭的错误，不记录为警告
                            error_msg = str(e).lower()
                            if "driver已关闭" in error_msg or "session" in error_msg:
                                self.logger.debug(f"WebDriver {aid} 已经关闭: {e}")
                            else:
                                self.logger.warning(f"关闭WebDriver {aid} 失败: {e}")

                    driver_tasks.append(close_driver(driver_wrapper, account_id))

                # 等待所有WebDriver关闭完成
                await asyncio.gather(*driver_tasks, return_exceptions=True)

                # 如果是强制清理或程序退出，执行彻底清理
                if force_cleanup:
                    self.logger.info("执行强制Chrome进程清理...")
                    for account_id in account_ids:
                        try:
                            self._complete_chrome_cleanup(account_id)
                        except Exception as e:
                            self.logger.warning(f"强制清理账号 {account_id} 失败: {e}")

            # 清理状态
            self.drivers.clear()

            # 清理其他状态
            if force_cleanup:
                self.protected_processes.clear()

            self.logger.info("所有WebDriver已关闭")

        except Exception as e:
            self.logger.error(f"关闭WebDriver失败: {e}")
            # 即使出错也要清理状态
            if force_cleanup:
                self.drivers.clear()
                self.protected_processes.clear()
            # 强制清理状态
            self.drivers.clear()
    
    def reset_browser_pool(self):
        """重置浏览器池状态"""
        try:
            # 清理所有状态
            self.drivers.clear()
            self.logger.info("浏览器池状态已重置")
        except Exception as e:
            self.logger.error(f"重置浏览器池失败: {e}")

    def sync_close_all(self, is_final_shutdown=False):
        """同步关闭所有WebDriver - 用于程序退出时避免线程阻塞"""
        try:
            # 🔧 修复：只在最终关闭时设置关闭标志
            if is_final_shutdown:
                self._shutdown = True
                # 停止自动清理服务
                self.stop_auto_cleanup()
                # 取消所有待处理的Chrome启动任务
                self._cancel_pending_futures()

            self.logger.info(f"开始同步关闭 {len(self.drivers)} 个WebDriver实例")

            # 同步关闭所有WebDriver
            for account_id, driver_wrapper in list(self.drivers.items()):
                try:
                    # 直接关闭WebDriver，不使用异步
                    if hasattr(driver_wrapper, 'driver') and driver_wrapper.driver:
                        try:
                            driver_wrapper.driver.quit()
                            self.logger.debug(f"WebDriver {account_id} 已关闭")
                        except Exception as e:
                            self.logger.debug(f"WebDriver {account_id} 关闭异常: {e}")

                    # 清理账号相关的Chrome进程
                    self._complete_chrome_cleanup(account_id)

                except Exception as e:
                    self.logger.warning(f"关闭WebDriver {account_id} 失败: {e}")

            # 清理状态
            self.drivers.clear()
            self.protected_processes.clear()

            # 最后执行简单的Chrome进程清理
            self.simple_chrome_cleanup()

            # 🔧 修复：如果是最终关闭，执行激进清理
            if is_final_shutdown:
                self._aggressive_chrome_cleanup()

            # 🔧 修复：关闭线程池
            self._shutdown_executor()

            self.logger.info("所有WebDriver已同步关闭")

        except Exception as e:
            self.logger.error(f"同步关闭WebDriver失败: {e}")
            # 即使出错也要清理状态
            self.drivers.clear()
            self.protected_processes.clear()
            self._shutdown = True
            self._shutdown_executor()

    def simple_chrome_cleanup(self):
        """精确的Chrome进程清理 - 只清理自动化Chrome"""
        try:
            import psutil

            self.logger.info("执行精确Chrome进程清理...")

            automation_count = 0
            user_count = 0

            # 使用psutil精确识别和清理
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    if not proc.info['name'] or 'chrome.exe' not in proc.info['name'].lower():
                        continue

                    # 🔧 使用方案2判断是否为自动化Chrome
                    if self._is_automation_chrome(proc):
                        # 是自动化Chrome，可以清理
                        self.logger.debug(f"清理自动化Chrome进程: PID={proc.info['pid']}")
                        proc.terminate()
                        automation_count += 1
                    else:
                        # 是用户Chrome，跳过保护
                        self.logger.debug(f"跳过用户Chrome进程: PID={proc.info['pid']}")
                        user_count += 1

                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
                except Exception as e:
                    self.logger.debug(f"处理Chrome进程失败: {e}")
                    continue

            # 清理ChromeDriver进程（这些通常都是自动化的）
            try:
                import subprocess
                subprocess.run(['taskkill', '/F', '/IM', 'chromedriver.exe'],
                             capture_output=True, timeout=3, text=True)
            except Exception:
                pass

            if automation_count > 0 or user_count > 0:
                self.logger.info(f"Chrome进程清理完成 - 清理自动化: {automation_count}, 保护用户: {user_count}")
            else:
                self.logger.debug("未发现Chrome进程")

        except Exception as e:
            self.logger.error(f"Chrome进程清理失败: {e}")

    def _cancel_pending_futures(self):
        """取消所有待处理的Future任务"""
        try:
            if self.pending_futures:
                self.logger.info(f"取消 {len(self.pending_futures)} 个待处理的Chrome启动任务")
                for future in list(self.pending_futures):
                    try:
                        future.cancel()
                    except Exception as e:
                        self.logger.debug(f"取消Future失败: {e}")
                self.pending_futures.clear()
        except Exception as e:
            self.logger.warning(f"取消待处理任务失败: {e}")

    def _shutdown_executor(self):
        """关闭线程池"""
        try:
            if hasattr(self, 'executor') and self.executor:
                self.logger.info("强制关闭浏览器管理器线程池")

                # 🔧 修复：强制终止所有线程，不等待任务完成
                import threading
                import time

                # 1. 立即关闭线程池，不等待
                self.executor.shutdown(wait=False)

                # 2. 强制杀死所有线程池线程
                for thread in threading.enumerate():
                    if thread.name.startswith("BrowserManager"):
                        self.logger.debug(f"强制终止线程: {thread.name}")
                        # 注意：Python没有直接杀死线程的方法，但设置关闭标志后线程应该会退出

                self.executor = None
                self.logger.info("线程池已强制关闭")
        except Exception as e:
            self.logger.warning(f"关闭线程池失败: {e}")

    def reset_for_restart(self):
        """重置浏览器池以便重新启动 - 用于停止任务后重新开始"""
        try:
            # 重置关闭标志，允许重新启动Chrome
            self._shutdown = False

            # 重新创建线程池（如果已关闭）
            if not hasattr(self, 'executor') or self.executor is None:
                from concurrent.futures import ThreadPoolExecutor
                self.executor = ThreadPoolExecutor(max_workers=3, thread_name_prefix="BrowserManager")

            # 清空待处理的Future
            self.pending_futures.clear()

            self.logger.info("浏览器池已重置，可以重新启动")
        except Exception as e:
            self.logger.warning(f"重置浏览器池失败: {e}")

    def _is_automation_chrome(self, process):
        """判断Chrome进程是否为自动化进程 - 基于用户数据目录"""
        try:
            cmdline = process.cmdline()
            if not cmdline:
                return False

            cmdline_str = ' '.join(cmdline)

            # 🔧 方案2：基于用户数据目录识别
            # 获取项目根目录的绝对路径
            import os
            from pathlib import Path
            project_root = Path(__file__).parent.parent.parent.absolute()
            project_path = str(project_root).replace('\\', '/')

            # 检查是否使用项目目录作为用户数据目录
            user_data_indicators = [
                f"--user-data-dir={project_path}",
                f"--user-data-dir={str(project_root)}",
                "browser_profiles",  # 我们的数据目录特征
                "account_"  # 我们的账号目录特征
            ]

            for indicator in user_data_indicators:
                if indicator in cmdline_str:
                    self.logger.debug(f"检测到自动化Chrome特征: {indicator}")
                    return True

            return False

        except Exception as e:
            self.logger.debug(f"检查Chrome进程失败: {e}")
            return False  # 有疑问的进程不清理

    def _aggressive_chrome_cleanup(self):
        """精确的Chrome进程清理 - 只清理自动化Chrome，保护用户Chrome"""
        try:
            import psutil

            self.logger.info("执行精确Chrome进程清理...")

            automation_count = 0
            user_count = 0

            # 使用psutil精确识别和清理
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    if not proc.info['name'] or 'chrome.exe' not in proc.info['name'].lower():
                        continue

                    # 🔧 使用方案2判断是否为自动化Chrome
                    if self._is_automation_chrome(proc):
                        # 是自动化Chrome，可以清理
                        self.logger.info(f"清理自动化Chrome进程: PID={proc.info['pid']}")
                        proc.terminate()
                        automation_count += 1
                    else:
                        # 是用户Chrome，跳过保护
                        self.logger.info(f"跳过用户Chrome进程: PID={proc.info['pid']}")
                        user_count += 1

                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
                except Exception as e:
                    self.logger.debug(f"处理Chrome进程失败: {e}")
                    continue

            self.logger.info(f"Chrome进程清理完成 - 清理自动化: {automation_count}, 保护用户: {user_count}")

        except Exception as e:
            self.logger.error(f"精确Chrome进程清理失败: {e}")

    async def get_driver_for_account(self, account: Account) -> Optional[SeleniumDriverWrapper]:
        """
        获取账号对应的WebDriver（兼容旧接口）

        Args:
            account: 账号对象

        Returns:
            WebDriver包装器
        """
        for attempt in range(3):
            try:
                driver_wrapper = await self.get_driver(account)
                if not driver_wrapper:
                    self.logger.error(f"无法获取WebDriver: {account.username}")
                    return None

                self.logger.debug(f"成功获取WebDriver: {account.username}")
                return driver_wrapper

            except Exception as e:
                self.logger.warning(f"获取WebDriver失败 (尝试 {attempt + 1}/3): {e}")
                if attempt < 2:  # 不是最后一次尝试
                    # 清理可能损坏的驱动
                    await self._cleanup_account_driver(account.id)
                    await asyncio.sleep(2)  # 等待2秒后重试
                continue

        self.logger.error(f"获取WebDriver最终失败: {account.username}")
        return None
    
    def get_driver_count(self) -> int:
        """获取当前WebDriver数量"""
        return len(self.drivers)

    def get_browser_count(self) -> int:
        """获取当前浏览器数量（兼容旧接口）"""
        return len(self.drivers)

    async def cleanup_inactive_drivers(self, inactive_hours: int = 2):
        """
        清理不活跃的WebDriver

        Args:
            inactive_hours: 不活跃小时数
        """
        try:
            cutoff_time = datetime.now() - timedelta(hours=inactive_hours)

            # 这里需要根据实际情况实现清理逻辑
            # 可以基于账号的最后活跃时间来判断

            self.logger.info("清理不活跃WebDriver完成")

        except Exception as e:
            self.logger.error(f"清理不活跃WebDriver失败: {e}")

    async def cleanup_inactive_browsers(self, inactive_hours: int = 2):
        """清理不活跃的浏览器（兼容旧接口）"""
        await self.cleanup_inactive_drivers(inactive_hours)

    def _is_connection_error(self, error) -> bool:
        """检查是否为Chrome连接错误"""
        try:
            error_str = str(error).lower()
            connection_indicators = [
                'cannot connect to chrome',
                'chrome not reachable',
                'connection refused',
                'session not created'
            ]
            return any(indicator in error_str for indicator in connection_indicators)
        except:
            return False

    def _can_auto_repair(self, account_id: int) -> bool:
        """检查是否可以执行自动修复"""
        try:
            # 初始化自动修复记录
            if not hasattr(self, 'last_auto_repair'):
                self.last_auto_repair = {}

            last_repair_time = self.last_auto_repair.get(account_id, 0)
            current_time = time.time()

            # 同一账号1小时内只能自动修复一次
            if current_time - last_repair_time < 3600:
                self.logger.debug(f"账号 {account_id} 自动修复冷却中")
                return False

            return True
        except:
            return False

    def _auto_fix_corrupted_profile(self, account, user_data_dir) -> bool:
        """自动检测和修复损坏的用户数据目录"""
        try:
            # 1. 检测目录是否损坏
            if not self._is_profile_corrupted(user_data_dir, account):
                self.logger.debug(f"用户数据目录未检测到损坏: {account.username}")
                return False

            # 2. 记录自动修复时间
            if not hasattr(self, 'last_auto_repair'):
                self.last_auto_repair = {}
            self.last_auto_repair[account.id] = time.time()

            # 3. 执行自动清理
            if self._clean_corrupted_profile(user_data_dir, account):
                self.logger.info(f"✅ 自动修复完成: {account.username}")
                self._log_auto_repair(account, "profile_corruption", "success", "目录已清理并重建")
                return True
            else:
                self.logger.error(f"❌ 自动修复失败: {account.username}")
                self._log_auto_repair(account, "profile_corruption", "failed", "目录清理失败")
                return False

        except Exception as e:
            self.logger.error(f"自动修复异常: {account.username}, 错误: {e}")
            self._log_auto_repair(account, "profile_corruption", "error", str(e))
            return False

    def _is_profile_corrupted(self, user_data_dir, account) -> bool:
        """检测用户数据目录是否损坏"""
        try:
            if not os.path.exists(user_data_dir):
                return False  # 目录不存在不算损坏

            self.logger.debug(f"检测用户数据目录: {user_data_dir}")
            corruption_indicators = []

            # 1. 检查关键文件是否存在且可读
            critical_files = ['Preferences', 'Local State']
            for file_name in critical_files:
                file_path = os.path.join(user_data_dir, file_name)
                if os.path.exists(file_path):
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            content = f.read(100)
                            if not content.strip():
                                corruption_indicators.append(f"{file_name}为空")
                    except Exception as e:
                        corruption_indicators.append(f"{file_name}无法读取: {str(e)[:50]}")

            # 2. 检查是否有锁定文件残留
            lock_files = ['SingletonLock', 'lockfile']
            for lock_file in lock_files:
                lock_path = os.path.join(user_data_dir, lock_file)
                if os.path.exists(lock_path):
                    try:
                        # 检查锁定文件是否过期（超过5分钟）
                        if time.time() - os.path.getmtime(lock_path) > 300:
                            corruption_indicators.append(f"过期锁定文件: {lock_file}")
                    except:
                        corruption_indicators.append(f"锁定文件异常: {lock_file}")

            # 3. 检查目录大小是否异常
            try:
                dir_size = self._get_directory_size(user_data_dir)
                if dir_size > 2 * 1024 * 1024 * 1024:  # 超过2GB才认为过大
                    corruption_indicators.append("目录过大")
                elif dir_size > 1024 * 1024 * 1024:  # 1-2GB之间给出警告但不认为损坏
                    self.logger.warning(f"用户目录较大: {account.username} - {dir_size/(1024*1024*1024):.1f}GB")
            except:
                pass

            # 如果有2个或以上的损坏指标，认为目录损坏
            if len(corruption_indicators) >= 2:  # 恢复合理门槛，需要2个或以上指标才认为损坏
                self.logger.info(f"🔍 检测到目录损坏指标: {account.username} - {corruption_indicators}")
                return True
            elif len(corruption_indicators) == 1:
                self.logger.debug(f"🔍 检测到轻微问题: {account.username} - {corruption_indicators}")
                return False

            return False

        except Exception as e:
            self.logger.debug(f"目录损坏检测失败: {e}")
            return False

    def _get_directory_size(self, directory):
        """获取目录大小"""
        total_size = 0
        try:
            for dirpath, dirnames, filenames in os.walk(directory):
                for filename in filenames:
                    filepath = os.path.join(dirpath, filename)
                    try:
                        total_size += os.path.getsize(filepath)
                    except:
                        pass
        except:
            pass
        return total_size

    def _clean_corrupted_profile(self, user_data_dir, account) -> bool:
        """清理损坏的用户数据目录"""
        try:
            self.logger.info(f"🧹 开始自动清理损坏目录: {account.username}")

            # 1. 强制结束可能的Chrome进程
            self._force_kill_chrome_processes(account.id)

            # 2. 尝试选择性清理而不是完全删除
            if self._selective_cleanup_profile(user_data_dir, account):
                return True

            # 3. 如果选择性清理失败，才删除整个目录
            import shutil
            if os.path.exists(user_data_dir):
                shutil.rmtree(user_data_dir, ignore_errors=True)

            # 3. 等待文件系统同步
            time.sleep(1)

            # 4. 验证删除是否成功
            if os.path.exists(user_data_dir):
                self.logger.warning(f"目录删除不完整，尝试强制删除: {user_data_dir}")
                try:
                    # 使用系统命令强制删除
                    import subprocess
                    subprocess.run(['rmdir', '/s', '/q', user_data_dir], shell=True, check=False)
                    time.sleep(2)
                except:
                    pass

            # 5. 记录清理结果
            if not os.path.exists(user_data_dir):
                self.logger.info(f"✅ 损坏目录清理成功: {account.username}")
                return True
            else:
                self.logger.error(f"❌ 损坏目录清理失败: {account.username}")
                return False

        except Exception as e:
            self.logger.error(f"自动清理异常: {e}")
            return False

    def _selective_cleanup_profile(self, user_data_dir, account) -> bool:
        """选择性清理用户目录，保留重要数据"""
        try:
            self.logger.info(f"🧹 尝试选择性清理: {account.username}")

            # 清理缓存目录
            cache_dirs = [
                'Default/Cache',
                'Default/Code Cache',
                'Default/GPUCache',
                'Default/Service Worker/CacheStorage',
                'Default/Application Cache'
            ]

            cleaned_size = 0
            for cache_dir in cache_dirs:
                cache_path = os.path.join(user_data_dir, cache_dir)
                if os.path.exists(cache_path):
                    try:
                        import shutil
                        dir_size = self._get_directory_size(cache_path)
                        shutil.rmtree(cache_path, ignore_errors=True)
                        cleaned_size += dir_size
                        self.logger.debug(f"清理缓存目录: {cache_dir} ({dir_size/(1024*1024):.1f}MB)")
                    except:
                        pass

            # 清理临时文件
            temp_patterns = ['*.tmp', '*.log', '*crash*', '*.dmp']
            for pattern in temp_patterns:
                import glob
                temp_files = glob.glob(os.path.join(user_data_dir, '**', pattern), recursive=True)
                for temp_file in temp_files:
                    try:
                        os.remove(temp_file)
                    except:
                        pass

            # 清理锁定文件
            lock_files = ['SingletonLock', 'lockfile']
            for lock_file in lock_files:
                lock_path = os.path.join(user_data_dir, lock_file)
                if os.path.exists(lock_path):
                    try:
                        os.remove(lock_path)
                        self.logger.debug(f"清理锁定文件: {lock_file}")
                    except:
                        pass

            if cleaned_size > 0:
                self.logger.info(f"✅ 选择性清理完成: {account.username} (清理 {cleaned_size/(1024*1024):.1f}MB)")
                return True

            return False

        except Exception as e:
            self.logger.debug(f"选择性清理失败: {e}")
            return False


    def _start_auto_cleanup(self):
        """启动自动清理服务"""
        try:
            # 延迟启动自动清理，确保数据库已初始化
            import threading
            def delayed_start():
                import time
                time.sleep(2)  # 等待2秒确保数据库初始化
                try:
                    from src.core.auto_cleanup_manager import get_auto_cleanup_manager
                    auto_cleanup = get_auto_cleanup_manager()
                    auto_cleanup.start_auto_cleanup()
                    self.logger.info("🧹 自动清理服务已启动")
                except Exception as e:
                    self.logger.warning(f"启动自动清理服务失败: {e}")
            
            thread = threading.Thread(target=delayed_start, daemon=True)
            thread.start()
            
        except Exception as e:
            self.logger.warning(f"启动自动清理服务失败: {e}")

    def stop_auto_cleanup(self):
        """停止自动清理服务"""
        try:
            from src.core.auto_cleanup_manager import get_auto_cleanup_manager
            auto_cleanup = get_auto_cleanup_manager()
            auto_cleanup.stop_auto_cleanup()
            self.logger.info("🧹 自动清理服务已停止")
        except Exception as e:
            self.logger.warning(f"停止自动清理服务失败: {e}")

    def _force_kill_chrome_processes(self, account_id):
        """强制结束Chrome进程"""
        try:
            import psutil
            import subprocess

            # 查找与该账号相关的Chrome进程
            user_data_pattern = f"account_{account_id}"
            killed_count = 0

            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    if proc.info['name'] and 'chrome' in proc.info['name'].lower():
                        cmdline = ' '.join(proc.info['cmdline'] or [])
                        if user_data_pattern in cmdline:
                            proc.kill()
                            killed_count += 1
                            self.logger.debug(f"强制结束Chrome进程: PID {proc.info['pid']}")
                except:
                    pass

            if killed_count > 0:
                self.logger.info(f"强制结束了 {killed_count} 个Chrome进程")
                time.sleep(2)  # 等待进程完全结束

        except Exception as e:
            self.logger.debug(f"强制结束Chrome进程失败: {e}")

    def _log_auto_repair(self, account, issue_type, result, details):
        """记录自动修复操作"""
        try:
            repair_log = {
                'timestamp': datetime.now().isoformat(),
                'account_id': account.id,
                'account_username': account.username,
                'issue_type': issue_type,
                'result': result,
                'details': details
            }

            # 写入修复日志文件
            log_dir = os.path.join(os.path.dirname(__file__), '..', '..', 'logs')
            os.makedirs(log_dir, exist_ok=True)
            log_file = os.path.join(log_dir, 'auto_repair.log')

            with open(log_file, 'a', encoding='utf-8') as f:
                f.write(f"{json.dumps(repair_log, ensure_ascii=False)}\n")

            self.logger.info(f"📝 自动修复日志已记录: {account.username} - {result}")

        except Exception as e:
            self.logger.debug(f"修复日志记录失败: {e}")


# 全局浏览器池实例
_browser_pool = None


def get_browser_pool() -> BrowserPool:
    """获取全局浏览器池实例"""
    global _browser_pool
    if _browser_pool is None:
        _browser_pool = BrowserPool()
    return _browser_pool
