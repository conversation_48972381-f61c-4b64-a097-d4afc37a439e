#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库连接管理模块
"""

import os
from pathlib import Path
from sqlalchemy import create_engine, text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import StaticPool
from loguru import logger

# 创建基础模型类
Base = declarative_base()


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self, database_url: str):
        """
        初始化数据库管理器
        
        Args:
            database_url: 数据库连接URL
        """
        self.database_url = database_url
        self.engine = None
        self.SessionLocal = None
        
    def init_database(self):
        """初始化数据库连接"""
        try:
            # 确保数据目录存在
            if self.database_url.startswith('sqlite:///'):
                db_path = self.database_url.replace('sqlite:///', '')
                db_dir = Path(db_path).parent
                db_dir.mkdir(parents=True, exist_ok=True)
            
            # 创建数据库引擎
            if 'sqlite' in self.database_url:
                self.engine = create_engine(
                    self.database_url,
                    poolclass=StaticPool,
                    connect_args={
                        "check_same_thread": False,
                        "timeout": 20
                    },
                    echo=False
                )
            else:
                self.engine = create_engine(self.database_url, echo=False)
            
            # 创建会话工厂
            self.SessionLocal = sessionmaker(
                autocommit=False,
                autoflush=False,
                bind=self.engine
            )
            
            # 创建所有表
            self.create_tables()
            
            logger.info(f"数据库连接初始化成功: {self.database_url}")
            
        except Exception as e:
            logger.error(f"数据库初始化失败: {e}")
            raise
    
    def create_tables(self):
        """创建所有数据表"""
        try:
            # 导入所有模型以确保它们被注册
            from .models import (
                Account, AccountGroup, Proxy, PostingTask,
                InteractionTask, MediaFile, Variable, OperationLog
            )
            
            # 创建所有表
            Base.metadata.create_all(bind=self.engine)
            
            # 创建索引
            self._create_indexes()
            
            logger.info("数据表创建完成")
            
        except Exception as e:
            logger.error(f"创建数据表失败: {e}")
            raise
    
    def _create_indexes(self):
        """创建数据库索引"""
        try:
            with self.engine.connect() as conn:
                # 账号表索引
                conn.execute(text(
                    "CREATE INDEX IF NOT EXISTS idx_accounts_username ON accounts(username)"
                ))
                conn.execute(text(
                    "CREATE INDEX IF NOT EXISTS idx_accounts_status ON accounts(status)"
                ))
                conn.execute(text(
                    "CREATE INDEX IF NOT EXISTS idx_accounts_group_id ON accounts(group_id)"
                ))
                
                # 任务表索引
                conn.execute(text(
                    "CREATE INDEX IF NOT EXISTS idx_posting_tasks_account_id ON posting_tasks(account_id)"
                ))
                conn.execute(text(
                    "CREATE INDEX IF NOT EXISTS idx_posting_tasks_status ON posting_tasks(status)"
                ))
                conn.execute(text(
                    "CREATE INDEX IF NOT EXISTS idx_interaction_tasks_post_url ON interaction_tasks(post_url)"
                ))
                
                # 日志表索引
                conn.execute(text(
                    "CREATE INDEX IF NOT EXISTS idx_operation_logs_created_at ON operation_logs(created_at)"
                ))
                conn.execute(text(
                    "CREATE INDEX IF NOT EXISTS idx_operation_logs_account_id ON operation_logs(account_id)"
                ))
                
                conn.commit()
                
            logger.info("数据库索引创建完成")
            
        except Exception as e:
            logger.warning(f"创建索引失败: {e}")
    
    def get_session(self) -> Session:
        """获取数据库会话"""
        if not self.SessionLocal:
            raise RuntimeError("数据库未初始化")
        return self.SessionLocal()
    
    def close(self):
        """关闭数据库连接"""
        if self.engine:
            self.engine.dispose()
            logger.info("数据库连接已关闭")


# 全局数据库管理器实例
db_manager = None


def get_db_manager() -> DatabaseManager:
    """获取全局数据库管理器实例"""
    global db_manager
    if not db_manager:
        raise RuntimeError("数据库管理器未初始化")
    return db_manager


def init_db_manager(database_url: str):
    """初始化全局数据库管理器"""
    global db_manager
    db_manager = DatabaseManager(database_url)
    db_manager.init_database()
    update_engine()  # 更新全局engine引用


def get_db_session() -> Session:
    """获取数据库会话的便捷函数"""
    return get_db_manager().get_session()


def get_engine():
    """获取数据库引擎"""
    global db_manager
    if db_manager is None:
        raise RuntimeError("数据库管理器未初始化")
    return db_manager.engine


# 为了兼容性，也导出engine
engine = None


def update_engine():
    """更新全局engine引用"""
    global engine, db_manager
    if db_manager and db_manager.engine:
        engine = db_manager.engine
        logger.debug("全局engine引用已更新")
