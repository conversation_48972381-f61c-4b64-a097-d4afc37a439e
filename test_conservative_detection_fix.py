#!/usr/bin/env python3
"""
保守检测修复测试
验证修复后的检测不会误判正常账号为暂停状态
"""

import sys
import asyncio
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


def test_conservative_suspension_detection():
    """测试保守的暂停检测"""
    print("🔍 测试保守的暂停检测...")
    
    try:
        from src.core.account_status_manager import AccountStatusManager
        
        manager = AccountStatusManager()
        
        # 测试用例：(页面内容, 预期结果, 描述)
        test_cases = [
            # 明确的暂停情况（应该检测为暂停）
            ("你的账号已被冻结 违反了X规则 可以申请复审", True, "明确的中文冻结提示"),
            ("Account suspended for violating rules. You can appeal this decision.", True, "明确的英文暂停提示"),
            ("Your account is suspended. Submit an appeal to review.", True, "英文暂停+申诉"),
            ("账号已被暂停 申请复审", True, "中文暂停+申诉"),
            
            # 正常情况（不应该检测为暂停）
            ("有什么新鲜事？ 主页时间线 推荐关注", False, "正常主页内容"),
            ("通知 全部 提及 已验证", False, "正常通知页面"),
            ("消息 发送消息", False, "正常消息页面"),
            ("设置 账户 隐私 安全", False, "正常设置页面"),
            ("Premium 可享 30% 优惠", False, "Premium广告"),
            ("What's happening? Home timeline", False, "英文正常主页"),
            
            # 边缘情况（不应该误判）
            ("账号设置 暂停通知", False, "设置中的暂停选项"),
            ("suspended animation movie", False, "包含suspended但无关"),
            ("appeal court decision", False, "法院申诉，非账号申诉"),
            ("", False, "空内容"),
            ("加载中...", False, "加载状态"),
            
            # 内容过少的情况（不应该误判）
            ("X", False, "内容过少"),
            ("Loading", False, "英文加载"),
            ("正在加载", False, "中文加载"),
        ]
        
        all_passed = True
        for content, expected, description in test_cases:
            # 使用保守检测方法（创建一个简单的mock driver_wrapper）
            class MockDriverWrapper:
                async def execute_async(self, func):
                    return "https://x.com/home"  # 模拟URL

            mock_driver = MockDriverWrapper()
            result = asyncio.run(manager._check_account_suspended_conservative(mock_driver, content))
            status = "✅" if result == expected else "❌"
            print(f"  {status} {description}")
            print(f"      内容: '{content[:50]}{'...' if len(content) > 50 else ''}'")
            print(f"      结果: {result} (预期: {expected})")
            print()

            if result != expected:
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 保守检测测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_page_content_length_handling():
    """测试页面内容长度处理"""
    print("\n📏 测试页面内容长度处理...")
    
    try:
        from src.core.account_status_manager import AccountStatusManager
        
        manager = AccountStatusManager()
        
        # 测试不同长度的内容
        test_cases = [
            # 内容过少的情况（应该跳过检测，返回False）
            ("", False, "空内容"),
            ("X", False, "单字符"),
            ("Loading...", False, "短加载文本"),
            ("正在加载页面", False, "中文短文本"),
            ("a" * 30, False, "30字符重复内容"),
            
            # 内容足够但正常的情况
            ("有什么新鲜事？这里是X平台的主页，您可以发布推文、查看时间线、关注其他用户。" * 2, False, "正常长内容"),
            ("What's happening? This is the X platform homepage where you can post tweets." * 3, False, "英文正常长内容"),
            
            # 内容足够且确实暂停的情况
            ("你的账号已被冻结，因为违反了X平台的使用规则。您可以通过申请复审来恢复账号。" * 2, True, "长暂停内容"),
        ]
        
        all_passed = True
        for content, expected, description in test_cases:
            # 创建mock driver wrapper
            class MockDriverWrapper:
                async def execute_async(self, func):
                    return "https://x.com/home"

            mock_driver = MockDriverWrapper()
            result = asyncio.run(manager._check_account_suspended_conservative(mock_driver, content))
            status = "✅" if result == expected else "❌"
            print(f"  {status} {description}")
            print(f"      长度: {len(content)} 字符")
            print(f"      内容: '{content[:50]}{'...' if len(content) > 50 else ''}'")
            print(f"      结果: {result} (预期: {expected})")
            print()

            if result != expected:
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 内容长度测试失败: {e}")
        return False


def test_url_detection_only():
    """测试仅URL检测的可靠性"""
    print("\n🌐 测试URL检测的可靠性...")
    
    try:
        from src.core.account_status_manager import AccountStatusManager
        
        manager = AccountStatusManager()
        
        # 测试URL检测
        test_cases = [
            # 明确的暂停URL（应该检测为暂停）
            ("https://x.com/account/suspended", True, "明确暂停URL"),
            ("https://x.com/i/flow/suspended", True, "暂停流程URL"),
            ("https://x.com/account/appeal", True, "申诉URL"),
            
            # 正常URL（不应该检测为暂停）
            ("https://x.com/home", False, "主页URL"),
            ("https://x.com/notifications", False, "通知URL"),
            ("https://x.com/messages", False, "消息URL"),
            ("https://x.com/settings", False, "设置URL"),
            ("https://x.com/explore", False, "探索URL"),
        ]
        
        all_passed = True
        for url, expected, description in test_cases:
            result = asyncio.run(manager._check_suspension_by_url(url))
            status = "✅" if result == expected else "❌"
            print(f"  {status} {description}: {url}")
            print(f"      结果: {result} (预期: {expected})")
            
            if result != expected:
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ URL检测测试失败: {e}")
        return False


def test_real_scenarios_conservative():
    """测试真实场景的保守检测"""
    print("\n🌍 测试真实场景的保守检测...")
    
    # 模拟真实的页面内容
    real_scenarios = [
        {
            "content": "你的账号已被冻结 有什么新鲜事？ Premium 可享 30% 优惠 主页 通知 消息 探索 书签 社群 Premium 个人资料 更多",
            "expected": True,
            "description": "用户实际情况：主页显示冻结但有其他内容"
        },
        {
            "content": "有什么新鲜事？ 主页 通知 消息 探索 书签 社群 Premium 个人资料 更多 推荐关注 热门话题",
            "expected": False,
            "description": "正常主页：完整的导航和内容"
        },
        {
            "content": "通知 全部 提及 已验证 设置 推荐关注 热门话题",
            "expected": False,
            "description": "正常通知页面"
        },
        {
            "content": "消息 发送消息 搜索消息 设置",
            "expected": False,
            "description": "正常消息页面"
        },
        {
            "content": "设置 你的账户 隐私和安全 通知 辅助功能、显示和语言 其他资源",
            "expected": False,
            "description": "正常设置页面"
        },
        {
            "content": "Account suspended for violating the X Rules. You can appeal this decision by submitting a request.",
            "expected": True,
            "description": "英文暂停页面"
        },
        {
            "content": "What's happening? Home Notifications Messages Explore Bookmarks Communities Premium Profile More",
            "expected": False,
            "description": "英文正常主页"
        }
    ]
    
    try:
        from src.core.account_status_manager import AccountStatusManager
        manager = AccountStatusManager()
        
        all_passed = True
        for scenario in real_scenarios:
            content = scenario["content"]
            expected = scenario["expected"]
            description = scenario["description"]
            
            result = asyncio.run(manager._check_account_suspended_conservative(None, content))
            
            status = "✅" if result == expected else "❌"
            print(f"  {status} {description}")
            print(f"      内容: '{content[:80]}{'...' if len(content) > 80 else ''}'")
            print(f"      结果: {result} (预期: {expected})")
            print()
            
            if result != expected:
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 真实场景测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("="*60)
    print("保守检测修复验证")
    print("="*60)
    print("🎯 目标: 修复误判问题，确保正常账号不被误判为暂停")
    print("🔧 策略: 保守检测 + 页面加载等待 + 明确短语优先")
    print("="*60)
    
    tests = [
        ("保守暂停检测", test_conservative_suspension_detection),
        ("页面内容长度处理", test_page_content_length_handling),
        ("URL检测可靠性", test_url_detection_only),
        ("真实场景保守检测", test_real_scenarios_conservative)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            results.append((test_name, False))
    
    # 汇总结果
    print(f"\n{'='*60}")
    print("保守检测修复验证结果")
    print(f"{'='*60}")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 通过")
    
    if passed == total:
        print("\n🎉 保守检测修复成功！")
        print("\n📋 修复内容:")
        print("1. ✅ 增加页面加载等待机制")
        print("2. ✅ 实现保守的暂停检测策略")
        print("3. ✅ 提高暂停判断的门槛")
        print("4. ✅ 处理页面内容过少的情况")
        print("5. ✅ 移除容易误判的检测方法")
        
        print("\n💡 修复效果:")
        print("- ✅ 正常账号不会被误判为暂停")
        print("- ✅ 页面加载完成后才进行检测")
        print("- ✅ 只有明确的暂停指示才判定为暂停")
        print("- ✅ 内容过少时跳过检测，避免误判")
        
        print("\n🔧 技术改进:")
        print("- 页面加载等待：确保内容完整加载")
        print("- 保守检测策略：只检测最明确的暂停指示")
        print("- 内容长度验证：过少内容不进行判断")
        print("- 移除激进方法：不再使用功能/结构/行为检测")
        
    else:
        print("\n⚠️  部分测试失败，可能还需要进一步调整")
    
    return passed == total


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n用户中断测试")
        sys.exit(1)
    except Exception as e:
        print(f"\n测试异常: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
