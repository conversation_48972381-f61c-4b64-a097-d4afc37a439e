#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🎭 表情符号输入修复测试脚本
测试修复后的表情符号输入功能
"""

import asyncio
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def _is_emoji_or_special(char: str) -> bool:
    """🎭 检查是否为表情符号或特殊字符"""
    if not char:
        return False

    char_code = ord(char)

    # 表情符号和特殊字符的Unicode范围（更全面）
    special_ranges = [
        (0x1F600, 0x1F64F),  # 表情符号
        (0x1F300, 0x1F5FF),  # 杂项符号和象形文字
        (0x1F680, 0x1F6FF),  # 交通和地图符号
        (0x1F1E0, 0x1F1FF),  # 区域指示符号
        (0x2600, 0x26FF),    # 杂项符号
        (0x2700, 0x27BF),    # 装饰符号
        (0x1F900, 0x1F9FF),  # 补充符号和象形文字
        (0x1F018, 0x1F270),  # 各种符号
        (0x238C, 0x2454),    # 杂项技术符号
        (0x20D0, 0x20FF),    # 组合变音符号
    ]

    # 检查是否在特殊字符范围内，或者超出BMP范围
    is_special = (char_code > 0xFFFF or
                 any(start <= char_code <= end for start, end in special_ranges))

    return is_special

def _smart_text_segmentation(text: str) -> list:
    """🧠 智能文本分段 - 避免重复问题"""
    segments = []
    current_segment = {'type': 'text', 'content': ''}

    i = 0
    while i < len(text):
        char = text[i]

        # 检查是否为表情符号
        if _is_emoji_or_special(char):
            # 保存当前文本段
            if current_segment['content']:
                segments.append(current_segment)
                current_segment = {'type': 'text', 'content': ''}

            # 收集连续的表情符号
            emoji_content = ''
            while i < len(text) and _is_emoji_or_special(text[i]):
                emoji_content += text[i]
                i += 1

            # 添加表情符号段
            segments.append({'type': 'emoji', 'content': emoji_content})
            continue
        else:
            current_segment['content'] += char
            i += 1

    # 添加最后的文本段
    if current_segment['content']:
        segments.append(current_segment)

    return segments

def _validate_emoji_preservation(original_text: str, actual_text: str) -> bool:
    """🎭 验证表情符号是否被保留"""
    if not original_text or not actual_text:
        return False

    # 提取原文本中的表情符号
    original_emojis = [char for char in original_text if _is_emoji_or_special(char)]
    actual_emojis = [char for char in actual_text if _is_emoji_or_special(char)]

    # 检查表情符号是否保留 - 如果原文有表情符号，实际文本也应该有
    if len(original_emojis) > 0:
        emoji_preserved = len(actual_emojis) >= len(original_emojis) * 0.5  # 至少保留50%
    else:
        emoji_preserved = True  # 原文没有表情符号，不需要验证

    # 检查文本长度是否合理（避免重复）
    length_reasonable = len(actual_text) <= len(original_text) * 1.5

    # 检查是否包含主要内容
    original_clean = original_text.replace('\n', '').replace('\r', '').strip()
    actual_clean = actual_text.replace('\n', '').replace('\r', '').strip()
    content_preserved = len(actual_clean) >= len(original_clean) * 0.7

    result = emoji_preserved and length_reasonable and content_preserved

    return result

def test_emoji_detection():
    """🔍 测试表情符号检测功能"""
    print("🔍 测试表情符号检测功能...")
    
    test_cases = [
        ('😊', True, '笑脸表情'),
        ('🤩', True, '星星眼表情'),
        ('🌻', True, '向日葵表情'),
        ('✨', True, '闪光表情'),
        ('又', False, '中文字符'),
        ('是', False, '中文字符'),
        ('美', False, '中文字符'),
        ('好', False, '中文字符'),
        ('的', False, '中文字符'),
        ('一', False, '中文字符'),
        ('天', False, '中文字符'),
        ('你', False, '中文字符'),
        ('们', False, '中文字符'),
        ('在', False, '中文字符'),
        ('吗', False, '中文字符'),
        ('\n', False, '换行符'),
        (' ', False, '空格'),
    ]
    
    success_count = 0
    for char, expected, description in test_cases:
        is_emoji = _is_emoji_or_special(char)
        unicode_code = f"U+{ord(char):04X}"
        status = "✅" if is_emoji == expected else "❌"
        
        print(f"{status} '{char}' ({unicode_code}) - {description}: 检测为表情符号={is_emoji}, 期望={expected}")
        
        if is_emoji == expected:
            success_count += 1
    
    print(f"\n检测准确率: {success_count}/{len(test_cases)} ({success_count/len(test_cases)*100:.1f}%)")
    return success_count == len(test_cases)

def test_text_segmentation():
    """🧠 测试智能文本分段功能"""
    print("\n🧠 测试智能文本分段功能...")
    
    test_text = "😊🤩\n\n又是美好的一天\n\n你们在吗🌻✨"
    
    segments = _smart_text_segmentation(test_text)
    
    print(f"原文本: '{test_text}'")
    print(f"分段结果: {len(segments)} 个段落")
    
    for i, segment in enumerate(segments):
        print(f"  段落 {i+1}: 类型={segment['type']}, 内容='{segment['content']}'")
    
    # 验证分段结果
    expected_segments = [
        {'type': 'emoji', 'content': '😊🤩'},
        {'type': 'text', 'content': '\n\n又是美好的一天\n\n你们在吗'},
        {'type': 'emoji', 'content': '🌻✨'}
    ]
    
    if len(segments) == len(expected_segments):
        print("✅ 分段数量正确")
        return True
    else:
        print(f"❌ 分段数量错误: 期望{len(expected_segments)}, 实际{len(segments)}")
        return False

def test_emoji_preservation_validation():
    """🎭 测试表情符号保留验证功能"""
    print("\n🎭 测试表情符号保留验证功能...")
    
    test_cases = [
        # (原文本, 实际文本, 期望结果, 描述)
        ("😊🤩\n\n又是美好的一天\n\n你们在吗🌻✨", "😊🤩\n\n又是美好的一天\n\n你们在吗🌻✨", True, "完全匹配"),
        ("😊🤩\n\n又是美好的一天\n\n你们在吗🌻✨", "😊🤩又是美好的一天你们在吗🌻✨", True, "表情符号保留，换行符丢失"),
        ("😊🤩\n\n又是美好的一天\n\n你们在吗🌻✨", "又是美好的一天你们在吗", False, "表情符号完全丢失"),
        ("😊🤩\n\n又是美好的一天\n\n你们在吗🌻✨", "😊又是美好的一天你们在吗🌻", True, "部分表情符号保留"),
        ("😊🤩\n\n又是美好的一天\n\n你们在吗🌻✨", "😊🤩又是美好的一天你们在吗🌻✨😊🤩又是美好的一天你们在吗🌻✨", False, "重复内容"),
    ]
    
    success_count = 0
    for original, actual, expected, description in test_cases:
        result = _validate_emoji_preservation(original, actual)
        status = "✅" if result == expected else "❌"
        
        print(f"{status} {description}: 验证结果={result}, 期望={expected}")
        
        if result == expected:
            success_count += 1
    
    print(f"\n验证准确率: {success_count}/{len(test_cases)} ({success_count/len(test_cases)*100:.1f}%)")
    return success_count == len(test_cases)

def test_clipboard_functionality():
    """📋 测试剪贴板功能"""
    print("\n📋 测试剪贴板功能...")
    
    try:
        import pyperclip
        
        test_emoji_text = "😊🤩\n\n又是美好的一天\n\n你们在吗🌻✨"
        
        # 保存原剪贴板内容
        original = ""
        try:
            original = pyperclip.paste()
        except:
            pass
        
        # 测试复制表情符号文本
        pyperclip.copy(test_emoji_text)
        copied = pyperclip.paste()
        
        # 恢复原内容
        try:
            pyperclip.copy(original)
        except:
            pass
        
        if copied == test_emoji_text:
            print("✅ 剪贴板支持表情符号和换行符")
            return True
        else:
            print(f"❌ 剪贴板测试失败")
            print(f"   原文本: '{test_emoji_text}'")
            print(f"   复制结果: '{copied}'")
            return False
            
    except ImportError:
        print("❌ pyperclip 未安装，无法测试剪贴板功能")
        return False
    except Exception as e:
        print(f"❌ 剪贴板测试异常: {e}")
        return False

def main():
    """主测试函数"""
    print("🎭 表情符号输入修复测试开始...\n")
    
    tests = [
        ("表情符号检测", test_emoji_detection),
        ("文本分段", test_text_segmentation),
        ("表情符号保留验证", test_emoji_preservation_validation),
        ("剪贴板功能", test_clipboard_functionality),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"测试: {test_name}")
        print('='*50)
        
        try:
            if test_func():
                print(f"✅ {test_name} 测试通过")
                passed += 1
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    print(f"\n{'='*50}")
    print(f"测试总结: {passed}/{total} 通过 ({passed/total*100:.1f}%)")
    print('='*50)
    
    if passed == total:
        print("🎉 所有测试通过！表情符号输入修复成功！")
    else:
        print("⚠️ 部分测试失败，需要进一步修复")

if __name__ == "__main__":
    main()
