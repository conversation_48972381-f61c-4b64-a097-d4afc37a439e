#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
防封引擎模块 - Selenium版本
"""

import random
import asyncio
import hashlib
import time
from typing import Dict, Any, List, Optional
from selenium.webdriver.common.action_chains import <PERSON><PERSON>hains
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException, WebDriverException

from src.database.models import Proxy
from src.utils.logger import LoggerMixin
from src.utils.helpers import get_timezone_by_country, get_language_by_country
from src.config.constants import USER_AGENTS, SCREEN_RESOLUTIONS
from src.config.settings import get_settings


class FingerprintGenerator(LoggerMixin):
    """浏览器指纹生成器"""
    
    def __init__(self):
        self.settings = get_settings()
    
    def generate_user_agent(self) -> str:
        """生成随机用户代理"""
        return random.choice(USER_AGENTS)
    
    def generate_viewport(self) -> Dict[str, int]:
        """生成随机视口大小"""
        return random.choice(SCREEN_RESOLUTIONS)
    
    def generate_webgl_data(self) -> Dict[str, Any]:
        """生成WebGL数据"""
        vendors = [
            'Google Inc.',
            'Mozilla',
            'Apple Inc.',
            'Microsoft Corporation'
        ]
        
        renderers = [
            'ANGLE (Intel(R) HD Graphics 620 Direct3D11 vs_5_0 ps_5_0)',
            'ANGLE (NVIDIA GeForce GTX 1060 Direct3D11 vs_5_0 ps_5_0)',
            'ANGLE (AMD Radeon RX 580 Direct3D11 vs_5_0 ps_5_0)',
            'Intel(R) Iris(R) Plus Graphics 640'
        ]
        
        return {
            'vendor': random.choice(vendors),
            'renderer': random.choice(renderers),
            'version': f'OpenGL ES {random.choice(["2.0", "3.0"])} (ANGLE {random.randint(1, 9)}.{random.randint(0, 9)}.{random.randint(0, 999)})'
        }
    
    def generate_canvas_hash(self) -> str:
        """生成Canvas指纹哈希"""
        # 生成随机的canvas数据
        canvas_data = f"canvas_{random.randint(1000000, 9999999)}"
        return hashlib.md5(canvas_data.encode()).hexdigest()[:16]
    
    def generate_audio_fingerprint(self) -> str:
        """生成音频指纹"""
        audio_data = f"audio_{random.randint(100000, 999999)}"
        return hashlib.sha256(audio_data.encode()).hexdigest()[:32]
    
    def generate_font_list(self) -> List[str]:
        """生成字体列表"""
        base_fonts = [
            'Arial', 'Helvetica', 'Times New Roman', 'Courier New',
            'Verdana', 'Georgia', 'Palatino', 'Garamond',
            'Bookman', 'Comic Sans MS', 'Trebuchet MS', 'Arial Black'
        ]
        
        # 随机添加一些额外字体
        extra_fonts = [
            'Calibri', 'Cambria', 'Consolas', 'Corbel',
            'Franklin Gothic Medium', 'Lucida Console', 'Segoe UI',
            'Tahoma', 'Impact', 'Lucida Sans Unicode'
        ]
        
        # 随机选择字体
        selected_fonts = base_fonts.copy()
        selected_fonts.extend(random.sample(extra_fonts, random.randint(3, 8)))
        
        return selected_fonts
    
    def generate_plugin_list(self) -> List[Dict[str, str]]:
        """生成插件列表"""
        plugins = [
            {'name': 'Chrome PDF Plugin', 'filename': 'internal-pdf-viewer'},
            {'name': 'Chrome PDF Viewer', 'filename': 'mhjfbmdgcfjbbpaeojofohoefgiehjai'},
            {'name': 'Native Client', 'filename': 'internal-nacl-plugin'},
        ]
        
        # 随机添加一些插件
        optional_plugins = [
            {'name': 'Adobe Flash Player', 'filename': 'pepflashplayer.dll'},
            {'name': 'Java Deployment Toolkit', 'filename': 'npdeployJava1.dll'},
            {'name': 'Microsoft Silverlight', 'filename': 'npctrl.dll'},
        ]
        
        # 随机选择插件
        if random.random() < 0.7:  # 70%概率添加额外插件
            plugins.extend(random.sample(optional_plugins, random.randint(1, 2)))
        
        return plugins


class BehaviorSimulator(LoggerMixin):
    """人类行为模拟器"""
    
    def __init__(self):
        self.settings = get_settings()
        self.anti_detection_config = self.settings.anti_detection
    
    async def random_mouse_movement(self, driver_wrapper, steps: int = None):
        """
        随机鼠标移动

        Args:
            driver_wrapper: WebDriver包装器
            steps: 移动步数
        """
        try:
            if steps is None:
                steps = self.anti_detection_config['mouse_movement_steps']

            # 获取窗口大小
            window_size = await driver_wrapper.execute_async(
                lambda: driver_wrapper.driver.get_window_size()
            )

            # 确保坐标在安全范围内，留出边距
            safe_margin = 50
            max_x = max(100, window_size['width'] - safe_margin)
            max_y = max(100, window_size['height'] - safe_margin)

            current_x = random.randint(safe_margin, max_x)
            current_y = random.randint(safe_margin, max_y)

            for _ in range(random.randint(2, 3)):  # 减少移动次数
                target_x = random.randint(safe_margin, max_x)
                target_y = random.randint(safe_margin, max_y)

                # 生成贝塞尔曲线路径
                path_points = self._generate_mouse_path(
                    (current_x, current_y),
                    (target_x, target_y),
                    steps
                )

                # 沿路径移动 - 使用更安全的方式
                try:
                    for x, y in path_points:
                        # 确保坐标在有效范围内
                        x = max(safe_margin, min(x, max_x))
                        y = max(safe_margin, min(y, max_y))

                        await driver_wrapper.execute_async(
                            lambda: ActionChains(driver_wrapper.driver).move_to_element_with_offset(
                                driver_wrapper.driver.find_element("tag name", "body"), x, y
                            ).perform()
                        )
                        await asyncio.sleep(random.uniform(0.01, 0.03))
                        current_x, current_y = x, y
                except Exception as move_error:
                    self.logger.debug(f"鼠标移动步骤失败，跳过: {move_error}")
                    break

                await asyncio.sleep(random.uniform(0.1, 0.5))

        except Exception as e:
            self.logger.warning(f"随机鼠标移动失败: {e}")
    
    def _generate_mouse_path(self, start: tuple, end: tuple, steps: int) -> List[tuple]:
        """
        生成鼠标移动路径
        
        Args:
            start: 起始坐标
            end: 结束坐标
            steps: 步数
            
        Returns:
            路径点列表
        """
        points = []
        
        # 添加一些随机控制点来模拟人类移动
        control_points = []
        for i in range(random.randint(1, 3)):
            control_x = random.randint(
                min(start[0], end[0]) - 50,
                max(start[0], end[0]) + 50
            )
            control_y = random.randint(
                min(start[1], end[1]) - 50,
                max(start[1], end[1]) + 50
            )
            control_points.append((control_x, control_y))
        
        # 生成贝塞尔曲线点
        for i in range(steps):
            t = i / (steps - 1)
            
            if len(control_points) == 1:
                # 二次贝塞尔曲线
                x = (1-t)**2 * start[0] + 2*(1-t)*t * control_points[0][0] + t**2 * end[0]
                y = (1-t)**2 * start[1] + 2*(1-t)*t * control_points[0][1] + t**2 * end[1]
            else:
                # 线性插值
                x = start[0] + t * (end[0] - start[0])
                y = start[1] + t * (end[1] - start[1])
            
            # 添加一些噪声
            x += random.uniform(-2, 2)
            y += random.uniform(-2, 2)
            
            points.append((int(x), int(y)))
        
        return points
    
    async def random_scroll(self, driver_wrapper):
        """
        随机滚动

        Args:
            driver_wrapper: WebDriver包装器
        """
        try:
            scroll_count = random.randint(2, 5)

            for _ in range(scroll_count):
                # 随机滚动方向和距离
                delta_y = random.randint(-300, 300)

                # 使用JavaScript滚动
                await driver_wrapper.execute_script(f"window.scrollBy(0, {delta_y});")

                # 随机停顿
                pause_time = random.uniform(
                    self.anti_detection_config['scroll_pause_min'],
                    self.anti_detection_config['scroll_pause_max']
                )
                await asyncio.sleep(pause_time)

        except Exception as e:
            self.logger.warning(f"随机滚动失败: {e}")
    
    async def human_typing(self, driver_wrapper, selector: str, text: str):
        """
        模拟人类打字 - 支持表情符号和特殊字符

        Args:
            driver_wrapper: WebDriver包装器
            selector: 元素选择器
            text: 要输入的文本
        """
        try:
            # 查找元素并点击
            element = await driver_wrapper.find_element(By.CSS_SELECTOR, selector)
            await driver_wrapper.execute_async(element.click)

            # 彻底清空现有内容
            try:
                # 方法1: 标准clear
                await driver_wrapper.execute_async(element.clear)
                await asyncio.sleep(0.1)

                # 方法2: 全选+删除
                await driver_wrapper.execute_async(
                    lambda: ActionChains(driver_wrapper.driver)
                    .key_down(Keys.CONTROL)
                    .send_keys('a')
                    .key_up(Keys.CONTROL)
                    .send_keys(Keys.DELETE)
                    .perform()
                )
                await asyncio.sleep(0.1)

                # 方法3: JavaScript清空
                await driver_wrapper.execute_async(
                    lambda: driver_wrapper.driver.execute_script("""
                        arguments[0].value = '';
                        arguments[0].textContent = '';
                        arguments[0].innerText = '';
                        arguments[0].innerHTML = '';
                    """, element)
                )
                await asyncio.sleep(0.2)

            except Exception as clear_error:
                self.logger.warning(f"清空内容失败: {clear_error}")

            # 处理特殊字符和表情符号
            safe_text = self._sanitize_text_for_typing(text)

            # 🎯 智能输入策略选择
            if safe_text != text:
                self.logger.info("检测到特殊字符，使用JavaScript输入")
                success = await self._javascript_input_with_validation(driver_wrapper, element, text)
                if not success:
                    self.logger.warning("JavaScript输入失败，尝试备用方法")
                    await self._fallback_input_method(driver_wrapper, element, text)
            else:
                # 逐字符输入（仅限BMP字符）
                typing_success = True
                for char in safe_text:
                    try:
                        await driver_wrapper.execute_async(element.send_keys, char)

                        # 平衡的打字间隔
                        delay = random.uniform(0.08, 0.15)
                        await asyncio.sleep(delay)

                        # 偶尔停顿（模拟思考）
                        if random.random() < 0.03:
                            await asyncio.sleep(random.uniform(0.3, 1.0))

                    except Exception as char_error:
                        self.logger.warning(f"字符输入失败: {char}, 错误: {char_error}")
                        typing_success = False
                        break

                # 如果逐字符输入失败，使用JavaScript输入
                if not typing_success:
                    self.logger.info("逐字符输入失败，切换到JavaScript输入")
                    success = await self._javascript_input_with_validation(driver_wrapper, element, text)
                    if not success:
                        await self._fallback_input_method(driver_wrapper, element, text)

        except Exception as e:
            self.logger.warning(f"模拟打字失败: {e}")
            # 最后的备用方案：直接JavaScript输入
            try:
                element = await driver_wrapper.find_element(By.CSS_SELECTOR, selector)
                await self._javascript_input(driver_wrapper, element, text)
            except Exception as fallback_error:
                self.logger.error(f"备用输入方案也失败: {fallback_error}")

    def _sanitize_text_for_typing(self, text: str) -> str:
        """
        清理文本，移除ChromeDriver不支持的字符

        Args:
            text: 原始文本

        Returns:
            清理后的文本
        """
        try:
            # 检查每个字符是否在BMP范围内（U+0000到U+FFFF）
            safe_chars = []
            for char in text:
                # BMP字符范围：0x0000 到 0xFFFF
                if ord(char) <= 0xFFFF:
                    safe_chars.append(char)
                else:
                    # 记录被过滤的字符
                    self.logger.debug(f"过滤非BMP字符: {char} (U+{ord(char):04X})")

            return ''.join(safe_chars)

        except Exception as e:
            self.logger.warning(f"文本清理失败: {e}")
            return text

    async def _javascript_input_with_validation(self, driver_wrapper, element, text):
        """
        🔧 带验证的JavaScript输入方法 - 检查是否成功且无重叠

        Args:
            driver_wrapper: WebDriver包装器
            element: 目标元素
            text: 要输入的文本

        Returns:
            bool: 输入是否成功
        """
        try:
            # 执行JavaScript输入
            await self._javascript_input(driver_wrapper, element, text)

            # 🔍 验证输入结果
            await asyncio.sleep(0.5)  # 等待界面更新

            # 获取实际显示的文本
            actual_text = await driver_wrapper.execute_async(
                lambda: driver_wrapper.driver.execute_script(
                    "return arguments[0].value || arguments[0].textContent || arguments[0].innerText || '';",
                    element
                )
            )

            # 🔍 改进的验证逻辑 - 更宽松的条件避免误判
            if actual_text:
                # 检查是否有重复内容（重叠的标志）
                if len(actual_text) > len(text) * 2.0:  # 放宽到2倍，避免误判
                    self.logger.warning(f"检测到严重的文本重叠: 预期长度{len(text)}, 实际长度{len(actual_text)}")
                    return False

                # 🎯 更宽松的匹配条件
                text_clean = text.strip()
                actual_clean = actual_text.strip()

                # 检查多种匹配情况
                if (text_clean in actual_clean or
                    actual_clean in text_clean or
                    len(actual_clean) > 0):  # 只要有内容就认为成功
                    self.logger.info(f"JavaScript输入验证成功: 预期{len(text_clean)}字符, 实际{len(actual_clean)}字符")
                    return True
                else:
                    self.logger.warning(f"输入文本完全不匹配: 预期'{text_clean[:30]}...', 实际'{actual_clean[:30]}...'")
                    return False
            else:
                self.logger.warning("输入后元素为空")
                return False

        except Exception as e:
            self.logger.error(f"JavaScript输入验证失败: {e}")
            return False

    async def _javascript_input(self, driver_wrapper, element, text: str):
        """
        使用JavaScript输入文本（支持所有Unicode字符）

        Args:
            driver_wrapper: WebDriver包装器
            element: 目标元素
            text: 要输入的文本
        """
        try:
            # 🔧 安全的JavaScript输入脚本 - 避免重叠和锁定问题
            script = """
            const element = arguments[0];
            const text = arguments[1];

            // 🎯 Step 1: 安全获取焦点
            element.focus();
            element.click();

            // 🧹 Step 2: 安全清空 - 使用标准方法避免冲突
            // 全选现有内容
            if (element.select) {
                element.select();
            } else {
                // 对于contenteditable元素
                const selection = window.getSelection();
                const range = document.createRange();
                range.selectNodeContents(element);
                selection.removeAllRanges();
                selection.addRange(range);
            }

            // 删除选中内容
            document.execCommand('delete');

            // 🎪 Step 3: 安全设置内容 - 只设置主要属性
            if (element.tagName.toLowerCase() === 'textarea' ||
                element.tagName.toLowerCase() === 'input') {
                element.value = text;
            } else if (element.contentEditable === 'true') {
                element.textContent = text;
            } else {
                element.value = text;
            }

            // 🔄 Step 4: 最小必要事件触发 - 避免过度触发导致锁定
            const inputEvent = new InputEvent('input', {
                bubbles: true,
                cancelable: true,
                inputType: 'insertText',
                data: text
            });
            element.dispatchEvent(inputEvent);

            const changeEvent = new Event('change', {
                bubbles: true,
                cancelable: true
            });
            element.dispatchEvent(changeEvent);

            // 🎭 Step 5: React状态同步 - 最后处理
            if (element._valueTracker) {
                element._valueTracker.setValue(text);
            }

            // 🔍 Step 6: 验证并返回
            const actualValue = element.value || element.textContent || element.innerText || '';

            return actualValue;
            """

            result = await driver_wrapper.execute_async(
                lambda: driver_wrapper.driver.execute_script(script, element, text)
            )

            self.logger.info(f"JavaScript输入成功，返回值: {result}")

            # 额外验证：检查文本是否真的显示在界面上
            await asyncio.sleep(0.5)

            # 尝试触发界面更新
            await driver_wrapper.execute_async(
                lambda: driver_wrapper.driver.execute_script("""
                    const element = arguments[0];
                    // 模拟用户交互来触发界面更新
                    element.click();
                    element.focus();

                    // 发送一个空格然后删除，触发界面刷新
                    const spaceEvent = new KeyboardEvent('keydown', {
                        key: ' ',
                        code: 'Space',
                        bubbles: true
                    });
                    element.dispatchEvent(spaceEvent);

                    const backspaceEvent = new KeyboardEvent('keydown', {
                        key: 'Backspace',
                        code: 'Backspace',
                        bubbles: true
                    });
                    element.dispatchEvent(backspaceEvent);
                """, element)
            )

            # 模拟一些打字延迟以保持人类化
            await asyncio.sleep(random.uniform(0.5, 1.5))

        except Exception as e:
            self.logger.error(f"JavaScript输入失败: {e}")
            # 🚨 JavaScript失败时使用备用方法
            await self._fallback_input_method(driver_wrapper, element, text)

    async def _fallback_input_method(self, driver_wrapper, element, text):
        """🔧 备用输入方法 - 当JavaScript方法失败时使用"""
        try:
            self.logger.info("使用备用输入方法...")

            # 方法1: 清空并重新输入
            await driver_wrapper.execute_async(
                lambda: element.clear()
            )
            await asyncio.sleep(0.3)

            await driver_wrapper.execute_async(
                lambda: element.send_keys(text)
            )

            self.logger.info("备用输入方法成功")

        except Exception as e:
            self.logger.error(f"备用输入方法也失败: {e}")

            # 方法2: 最简单的直接设置
            try:
                await driver_wrapper.execute_async(
                    lambda: driver_wrapper.driver.execute_script(
                        "arguments[0].value = arguments[1]; arguments[0].dispatchEvent(new Event('input', {bubbles: true}));",
                        element, text
                    )
                )
                self.logger.info("最简单输入方法成功")
            except Exception as e2:
                self.logger.error(f"所有输入方法都失败: {e2}")

    async def random_delay(self):
        """人类化随机延迟 - 平衡性能和安全性"""
        delay = random.uniform(0.8, 2.0)  # 恢复人类化的延迟时间
        await asyncio.sleep(delay)


class AntiDetectionEngine(LoggerMixin):
    """防封引擎"""
    
    def __init__(self):
        self.fingerprint_generator = FingerprintGenerator()
        self.behavior_simulator = BehaviorSimulator()
    
    def generate_fingerprint(self, proxy: Optional[Proxy] = None) -> Dict[str, Any]:
        """
        生成浏览器指纹
        
        Args:
            proxy: 代理对象
            
        Returns:
            指纹字典
        """
        fingerprint = {
            'userAgent': self.fingerprint_generator.generate_user_agent(),
            'viewport': self.fingerprint_generator.generate_viewport(),
            'webgl': self.fingerprint_generator.generate_webgl_data(),
            'canvas': self.fingerprint_generator.generate_canvas_hash(),
            'audio': self.fingerprint_generator.generate_audio_fingerprint(),
            'fonts': self.fingerprint_generator.generate_font_list(),
            'plugins': self.fingerprint_generator.generate_plugin_list(),
            'battery': random.choice([True, False]),
            'hardwareConcurrency': random.choice([2, 4, 8, 16]),
            'deviceMemory': random.choice([4, 8, 16, 32]),
            'colorDepth': random.choice([24, 32]),
            'pixelRatio': random.choice([1, 1.25, 1.5, 2])
        }
        
        # 根据代理地区调整时区和语言
        if proxy and proxy.country:
            fingerprint['timezone'] = get_timezone_by_country(proxy.country)
            fingerprint['language'] = get_language_by_country(proxy.country)
        else:
            fingerprint['timezone'] = 'America/New_York'
            fingerprint['language'] = 'en-US'
        
        return fingerprint
    
    async def simulate_human_behavior(self, driver_wrapper):
        """
        模拟人类行为

        Args:
            driver_wrapper: WebDriver包装器
        """
        try:
            # 随机鼠标移动
            await self.behavior_simulator.random_mouse_movement(driver_wrapper)

            # 随机滚动
            await self.behavior_simulator.random_scroll(driver_wrapper)

            # 随机延迟
            await self.behavior_simulator.random_delay()

        except Exception as e:
            self.logger.warning(f"模拟人类行为失败: {e}")
    
    async def safe_click(self, driver_wrapper, selector: str):
        """
        安全点击（带人类行为模拟）

        Args:
            driver_wrapper: WebDriver包装器
            selector: 元素选择器
        """
        try:
            # 验证选择器是否有效
            if not selector or not isinstance(selector, str):
                raise ValueError(f"无效的选择器: {selector}")

            # 等待元素可见且可用
            wait = WebDriverWait(driver_wrapper.driver, 10)

            # 尝试多种选择器策略
            element = None
            try:
                element = await driver_wrapper.execute_async(
                    lambda: wait.until(EC.element_to_be_clickable((By.CSS_SELECTOR, selector)))
                )
            except Exception:
                # 如果CSS选择器失败，尝试XPath
                try:
                    element = await driver_wrapper.execute_async(
                        lambda: wait.until(EC.element_to_be_clickable((By.XPATH, selector)))
                    )
                except Exception:
                    # 最后尝试ID或name
                    try:
                        element = await driver_wrapper.execute_async(
                            lambda: wait.until(EC.element_to_be_clickable((By.ID, selector)))
                        )
                    except Exception:
                        element = await driver_wrapper.execute_async(
                            lambda: wait.until(EC.element_to_be_clickable((By.NAME, selector)))
                        )

            if not element:
                raise Exception(f"无法找到元素: {selector}")

            # 模拟鼠标移动到元素
            try:
                await driver_wrapper.execute_async(
                    lambda: ActionChains(driver_wrapper.driver).move_to_element(element).perform()
                )
                await asyncio.sleep(random.uniform(0.1, 0.3))
            except Exception as move_error:
                self.logger.debug(f"鼠标移动失败，直接点击: {move_error}")

            # 点击
            await driver_wrapper.execute_async(element.click)

            # 随机延迟
            await self.behavior_simulator.random_delay()

        except Exception as e:
            self.logger.warning(f"安全点击失败: {e}")
            # 不再抛出异常，而是返回False表示失败
            return False

        return True
    
    async def safe_type(self, driver_wrapper, selector: str, text: str):
        """
        安全输入文本（带人类行为模拟）

        Args:
            driver_wrapper: WebDriver包装器
            selector: 元素选择器
            text: 要输入的文本
        """
        try:
            # 验证选择器是否有效
            if not selector or not isinstance(selector, str):
                raise ValueError(f"无效的选择器: {selector}")

            # 等待元素可见，尝试多种选择器策略
            wait = WebDriverWait(driver_wrapper.driver, 15)
            element = None

            try:
                # 首先尝试CSS选择器
                element = await driver_wrapper.execute_async(
                    lambda: wait.until(EC.element_to_be_clickable((By.CSS_SELECTOR, selector)))
                )
            except Exception:
                # 如果CSS选择器失败，尝试其他策略
                try:
                    element = await driver_wrapper.execute_async(
                        lambda: wait.until(EC.element_to_be_clickable((By.XPATH, selector)))
                    )
                except Exception:
                    try:
                        element = await driver_wrapper.execute_async(
                            lambda: wait.until(EC.element_to_be_clickable((By.NAME, selector)))
                        )
                    except Exception:
                        element = await driver_wrapper.execute_async(
                            lambda: wait.until(EC.element_to_be_clickable((By.ID, selector)))
                        )

            if not element:
                raise Exception(f"无法找到元素: {selector}")

            # 滚动到元素可见
            try:
                await driver_wrapper.execute_async(
                    lambda: driver_wrapper.driver.execute_script("arguments[0].scrollIntoView(true);", element)
                )
                await asyncio.sleep(0.5)
            except Exception as scroll_error:
                self.logger.debug(f"滚动到元素失败: {scroll_error}")

            # 点击元素获得焦点
            await driver_wrapper.execute_async(element.click)
            await asyncio.sleep(0.2)

            # 清空现有内容
            try:
                await driver_wrapper.execute_async(element.clear)
            except Exception:
                # 如果clear失败，尝试选择全部然后删除
                try:
                    await driver_wrapper.execute_async(
                        lambda: element.send_keys(Keys.CONTROL + "a")
                    )
                    await asyncio.sleep(0.1)
                    await driver_wrapper.execute_async(
                        lambda: element.send_keys(Keys.DELETE)
                    )
                except Exception as clear_error:
                    self.logger.debug(f"清空输入框失败: {clear_error}")

            # 🎭 使用完全模拟人工输入
            await self._simulate_pure_human_input(driver_wrapper, element, text)

        except Exception as e:
            self.logger.warning(f"安全输入失败 (选择器: {selector}): {e}")
            raise

    async def _simulate_pure_human_input(self, driver_wrapper, element, text: str):
        """
        🎭 完全模拟人工输入 - 包括表情符号和特殊字符

        Args:
            driver_wrapper: WebDriver包装器
            element: 目标元素
            text: 要输入的文本
        """
        try:
            self.logger.info(f"🎭 开始纯人工输入模拟: '{text[:50]}...'")

            # 🧹 彻底清空输入框，避免重复内容
            await self._thorough_clear_input(driver_wrapper, element)

            # 🎯 优先尝试一次性输入完整文案
            success = await self._try_complete_input_methods(driver_wrapper, element, text)

            if not success:
                # 🔄 备用方案：改进的分段输入
                self.logger.warning("一次性输入失败，使用改进的分段输入方案")
                await self._improved_segmented_input(driver_wrapper, element, text)

            # 🎭 输入完成后的自然行为
            await self._post_input_natural_behavior(driver_wrapper, element)

            self.logger.info("✅ 纯人工输入模拟完成")

        except Exception as e:
            self.logger.error(f"纯人工输入模拟失败: {e}")
            raise

    async def _gentle_clear_input(self, driver_wrapper, element):
        """🧹 温和清空输入框 - 模拟用户全选删除"""
        try:
            self.logger.debug("🧹 模拟用户清空输入框...")

            # 确保焦点在元素上
            await driver_wrapper.execute_async(element.click)
            await asyncio.sleep(random.uniform(0.2, 0.4))

            # 模拟用户按Ctrl+A全选
            await driver_wrapper.execute_async(
                lambda: ActionChains(driver_wrapper.driver)
                .key_down(Keys.CONTROL)
                .send_keys('a')
                .key_up(Keys.CONTROL)
                .perform()
            )
            await asyncio.sleep(random.uniform(0.1, 0.3))

            # 模拟用户按Delete删除
            await driver_wrapper.execute_async(
                lambda: ActionChains(driver_wrapper.driver)
                .send_keys(Keys.DELETE)
                .perform()
            )
            await asyncio.sleep(random.uniform(0.2, 0.5))

        except Exception as e:
            self.logger.debug(f"温和清空失败: {e}")

    async def _type_like_human(self, driver_wrapper, element, text: str):
        """🎯 像人类一样打字 - 优先使用整体输入策略"""
        try:
            # 🎯 首先尝试一次性输入整个文案（最可靠的方法）
            self.logger.info(f"🎯 尝试一次性输入整个文案")
            success = await self._input_complete_text(driver_wrapper, element, text)

            if success:
                self.logger.info("✅ 一次性输入成功")
                return

            # 🔄 备用方案：分段输入
            self.logger.warning("一次性输入失败，使用分段输入备用方案")
            text_segments = self._group_emoji_sequences(text)

            for i, segment in enumerate(text_segments):
                # 🎪 模拟人类的不规律打字
                if i > 0 and random.random() < 0.08:  # 8%概率停顿思考
                    pause_time = random.uniform(0.5, 2.0)
                    self.logger.debug(f"模拟思考停顿: {pause_time:.2f}秒")
                    await asyncio.sleep(pause_time)

                # 🔤 输入文本段
                await self._input_text_segment(driver_wrapper, element, segment)

                # 🕐 人类打字间隔
                if segment['type'] == 'emoji':
                    delay = random.uniform(0.3, 0.8)  # 表情符号输入后稍长停顿
                else:
                    delay = self._get_human_typing_delay(segment['content'][-1] if segment['content'] else ' ')
                await asyncio.sleep(delay)

        except Exception as e:
            self.logger.error(f"人类打字模拟失败: {e}")
            raise

    async def _thorough_clear_input(self, driver_wrapper, element):
        """🧹 彻底清空输入框，避免重复内容"""
        try:
            # 方法1: 全选+删除
            await driver_wrapper.execute_async(element.click)
            await asyncio.sleep(0.1)

            await driver_wrapper.execute_async(
                lambda: ActionChains(driver_wrapper.driver)
                .key_down(Keys.CONTROL)
                .send_keys('a')
                .key_up(Keys.CONTROL)
                .send_keys(Keys.DELETE)
                .perform()
            )
            await asyncio.sleep(0.2)

            # 方法2: JavaScript清空
            await driver_wrapper.execute_async(
                lambda: driver_wrapper.driver.execute_script(
                    """
                    const element = arguments[0];
                    element.value = '';
                    element.textContent = '';
                    element.innerText = '';
                    // 触发input事件
                    element.dispatchEvent(new Event('input', { bubbles: true }));
                    element.dispatchEvent(new Event('change', { bubbles: true }));
                    """, element
                )
            )
            await asyncio.sleep(0.1)

            self.logger.debug("✅ 输入框已彻底清空")

        except Exception as e:
            self.logger.warning(f"清空输入框失败: {e}")

    async def _try_complete_input_methods(self, driver_wrapper, element, text: str):
        """🎯 尝试一次性输入完整文案的多种方法 - 根据运行环境优化"""

        # 检查是否为无头模式
        is_headless = self._is_headless_mode()

        if is_headless:
            # 无头模式：使用专门的无头模式方法
            methods = [
                ("无头模式JavaScript方法", self._headless_js_input),
                ("无头模式分段输入", self._headless_segmented_input),
            ]
        else:
            # 有头模式：使用原有方法
            methods = [
                ("改进剪贴板方法", self._improved_clipboard_input),
                ("改进JavaScript方法", self._improved_js_input),
            ]

        for method_name, method in methods:
            try:
                self.logger.info(f"🔄 尝试{method_name}输入完整文案")
                success = await method(driver_wrapper, element, text)
                if success:
                    self.logger.info(f"✅ {method_name}成功")
                    return True
                else:
                    self.logger.warning(f"❌ {method_name}失败")
            except Exception as e:
                self.logger.warning(f"❌ {method_name}异常: {e}")
                continue

        return False

    async def _improved_segmented_input(self, driver_wrapper, element, text: str):
        """🔄 改进的分段输入 - 避免重复问题"""
        try:
            # 再次确保输入框为空
            await self._thorough_clear_input(driver_wrapper, element)

            # 智能分段：表情符号单独处理，文本按行分段
            segments = self._smart_text_segmentation(text)

            for i, segment in enumerate(segments):
                self.logger.info(f"🔤 输入段落 {i+1}/{len(segments)}: '{segment['content'][:20]}...' (类型: {segment['type']})")

                # 验证当前输入框状态，避免重复
                current_content = await self._get_current_input_content(driver_wrapper, element)
                if current_content and len(current_content) > len(text) * 0.8:
                    self.logger.warning("检测到可能的重复输入，停止分段输入")
                    break

                # 根据段落类型选择输入方法
                if segment['type'] == 'emoji':
                    await self._input_emoji_segment_safely(driver_wrapper, element, segment['content'])
                else:
                    await self._input_text_segment_safely(driver_wrapper, element, segment['content'])

                # 段落间停顿
                if i < len(segments) - 1:
                    await asyncio.sleep(random.uniform(0.2, 0.5))

        except Exception as e:
            self.logger.error(f"改进分段输入失败: {e}")
            raise

    async def _input_complete_text(self, driver_wrapper, element, text: str):
        """🎯 一次性输入完整文案 - 根据运行环境选择最佳方法"""

        # 检查是否为无头模式
        is_headless = self._is_headless_mode()

        if is_headless:
            # 无头模式：避免使用剪贴板，优先使用JavaScript
            methods = [
                ("无头模式JavaScript方法", self._headless_js_input),
                ("备用分段输入", self._headless_segmented_input),
            ]
        else:
            # 有头模式：使用原有方法
            methods = [
                ("剪贴板方法", self._input_via_clipboard),
                ("JavaScript方法", self._input_emoji_via_js),
            ]

        for method_name, method in methods:
            try:
                self.logger.info(f"🔄 尝试{method_name}输入完整文案")
                success = await method(driver_wrapper, element, text)
                if success:
                    self.logger.info(f"✅ {method_name}成功输入完整文案")
                    return True
                else:
                    self.logger.warning(f"❌ {method_name}失败")
            except Exception as e:
                self.logger.warning(f"❌ {method_name}异常: {e}")
                continue

        self.logger.warning("❌ 所有一次性输入方法都失败了")
        return False

    def _is_headless_mode(self) -> bool:
        """检查是否为无头模式"""
        try:
            from src.config.settings import get_settings
            settings = get_settings()
            return settings.browser_headless
        except:
            return False

    async def _headless_js_input(self, driver_wrapper, element, text: str):
        """🤖 无头模式专用JavaScript输入方法"""
        try:
            self.logger.info("🤖 使用无头模式JavaScript输入")

            # 无头模式下的JavaScript输入，避免剪贴板依赖
            result = await driver_wrapper.execute_async(
                lambda: driver_wrapper.driver.execute_script("""
                    const element = arguments[0];
                    const text = arguments[1];

                    // 确保元素获得焦点
                    element.focus();

                    // 清空现有内容
                    element.value = '';
                    element.textContent = '';
                    element.innerText = '';

                    // 设置新内容 - 针对不同类型的元素
                    if (element.tagName.toLowerCase() === 'textarea' || element.type === 'text') {
                        element.value = text;
                    } else if (element.contentEditable === 'true') {
                        // 对于contenteditable元素，保持原始格式
                        element.textContent = text;
                    } else {
                        element.innerText = text;
                    }

                    // 触发所有必要的事件
                    const events = ['input', 'change', 'keyup', 'keydown'];
                    events.forEach(eventType => {
                        const event = new Event(eventType, {
                            bubbles: true,
                            cancelable: true
                        });
                        element.dispatchEvent(event);
                    });

                    // 设置光标位置到末尾
                    if (element.setSelectionRange) {
                        element.setSelectionRange(text.length, text.length);
                    } else if (element.createTextRange) {
                        const range = element.createTextRange();
                        range.collapse(false);
                        range.select();
                    }

                    // 返回最终内容进行验证
                    return element.value || element.textContent || element.innerText || '';
                """, element, text)
            )

            await asyncio.sleep(0.3)

            # 验证输入结果
            if result and len(result) >= len(text) * 0.8:
                self.logger.info(f"✅ 无头模式JavaScript输入成功: {len(result)}/{len(text)} 字符")
                return True
            else:
                self.logger.warning(f"❌ 无头模式JavaScript输入验证失败: '{result[:50]}...'")
                return False

        except Exception as e:
            self.logger.warning(f"无头模式JavaScript输入失败: {e}")
            return False

    async def _headless_segmented_input(self, driver_wrapper, element, text: str):
        """🤖 无头模式专用分段输入方法"""
        try:
            self.logger.info("🤖 使用无头模式分段输入")

            # 清空输入框
            await driver_wrapper.execute_async(
                lambda: driver_wrapper.driver.execute_script(
                    "arguments[0].value = ''; arguments[0].textContent = ''; arguments[0].innerText = '';",
                    element
                )
            )

            # 将文本分为表情符号和普通文本
            segments = self._smart_text_segmentation(text)

            for segment in segments:
                if segment['type'] == 'emoji':
                    # 表情符号使用JavaScript设置
                    await self._headless_input_emoji_segment(driver_wrapper, element, segment['content'])
                else:
                    # 普通文本使用JavaScript设置
                    await self._headless_input_text_segment(driver_wrapper, element, segment['content'])

                await asyncio.sleep(0.1)

            # 验证最终结果
            final_content = await driver_wrapper.execute_async(
                lambda: driver_wrapper.driver.execute_script(
                    'return arguments[0].value || arguments[0].textContent || arguments[0].innerText || "";',
                    element
                )
            )

            if final_content and len(final_content) >= len(text) * 0.7:
                self.logger.info(f"✅ 无头模式分段输入成功: {len(final_content)}/{len(text)} 字符")
                return True
            else:
                self.logger.warning(f"❌ 无头模式分段输入验证失败")
                return False

        except Exception as e:
            self.logger.warning(f"无头模式分段输入失败: {e}")
            return False

    async def _headless_input_emoji_segment(self, driver_wrapper, element, emoji_content: str):
        """🤖 无头模式表情符号段输入"""
        try:
            await driver_wrapper.execute_async(
                lambda: driver_wrapper.driver.execute_script("""
                    const element = arguments[0];
                    const emoji = arguments[1];
                    const currentValue = element.value || element.textContent || element.innerText || '';
                    const newValue = currentValue + emoji;

                    if (element.tagName.toLowerCase() === 'textarea' || element.type === 'text') {
                        element.value = newValue;
                    } else {
                        element.textContent = newValue;
                    }

                    // 触发输入事件
                    element.dispatchEvent(new Event('input', { bubbles: true }));
                """, element, emoji_content)
            )
        except Exception as e:
            self.logger.debug(f"无头模式表情符号输入失败: {e}")

    async def _headless_input_text_segment(self, driver_wrapper, element, text_content: str):
        """🤖 无头模式文本段输入"""
        try:
            # 使用JavaScript直接设置文本内容
            await driver_wrapper.execute_async(
                lambda: driver_wrapper.driver.execute_script("""
                    const element = arguments[0];
                    const text = arguments[1];
                    const currentValue = element.value || element.textContent || element.innerText || '';
                    const newValue = currentValue + text;

                    if (element.tagName.toLowerCase() === 'textarea' || element.type === 'text') {
                        element.value = newValue;
                    } else {
                        element.textContent = newValue;
                    }

                    // 触发输入事件
                    element.dispatchEvent(new Event('input', { bubbles: true }));
                """, element, text_content)
            )
        except Exception as e:
            self.logger.debug(f"无头模式文本输入失败: {e}")

    async def _improved_clipboard_input(self, driver_wrapper, element, text: str):
        """📋 改进的剪贴板输入方法"""
        try:
            import pyperclip

            # 保存原剪贴板内容
            original_clipboard = ""
            try:
                original_clipboard = pyperclip.paste()
            except:
                pass

            # 复制到剪贴板
            pyperclip.copy(text)
            await asyncio.sleep(0.3)  # 增加等待时间

            # 确保元素获得焦点
            await driver_wrapper.execute_async(element.click)
            await asyncio.sleep(0.2)

            # 粘贴
            await driver_wrapper.execute_async(
                lambda: ActionChains(driver_wrapper.driver)
                .key_down(Keys.CONTROL)
                .send_keys('v')
                .key_up(Keys.CONTROL)
                .perform()
            )
            await asyncio.sleep(0.5)

            # 验证结果
            actual_content = await self._get_current_input_content(driver_wrapper, element)

            # 恢复剪贴板
            try:
                pyperclip.copy(original_clipboard)
            except:
                pass

            # 严格验证：检查表情符号是否保留
            if self._validate_emoji_preservation(text, actual_content):
                self.logger.info("✅ 改进剪贴板输入成功，表情符号已保留")
                return True
            else:
                self.logger.warning(f"❌ 剪贴板输入验证失败")
                return False

        except Exception as e:
            self.logger.warning(f"改进剪贴板输入失败: {e}")
            return False

    async def _improved_js_input(self, driver_wrapper, element, text: str):
        """🎭 改进的JavaScript输入方法 - 保留换行符和表情符号"""
        try:
            result = await driver_wrapper.execute_async(
                lambda: driver_wrapper.driver.execute_script("""
                    const element = arguments[0];
                    const text = arguments[1];

                    // 确保元素获得焦点
                    element.focus();

                    // 清空现有内容
                    element.value = '';
                    element.textContent = '';
                    element.innerText = '';

                    // 设置新内容 - 保留所有字符包括换行符
                    if (element.tagName.toLowerCase() === 'textarea' || element.type === 'text') {
                        element.value = text;
                    } else {
                        // 对于contenteditable元素，保留换行符
                        // 使用正确的换行符分割
                        const lines = text.split('\\n');
                        element.innerHTML = '';

                        for (let i = 0; i < lines.length; i++) {
                            if (lines[i]) {
                                const textNode = document.createTextNode(lines[i]);
                                element.appendChild(textNode);
                            }

                            // 添加换行符，除了最后一行
                            if (i < lines.length - 1) {
                                element.appendChild(document.createElement('br'));
                            }
                        }

                        // 如果文本以换行符结尾，添加额外的br
                        if (text.endsWith('\\n')) {
                            element.appendChild(document.createElement('br'));
                        }
                    }

                    // 触发事件
                    ['input', 'change', 'keyup'].forEach(eventType => {
                        const event = new Event(eventType, { bubbles: true });
                        element.dispatchEvent(event);
                    });

                    // 更新光标位置
                    if (element.setSelectionRange) {
                        element.setSelectionRange(text.length, text.length);
                    }

                    // 返回最终内容进行验证
                    return element.value || element.textContent || element.innerText || '';
                """, element, text)
            )

            await asyncio.sleep(0.3)

            # 验证结果
            if self._validate_emoji_preservation(text, result):
                self.logger.info("✅ 改进JavaScript输入成功，表情符号已保留")
                return True
            else:
                self.logger.warning(f"❌ JavaScript输入验证失败")
                return False

        except Exception as e:
            self.logger.warning(f"改进JavaScript输入失败: {e}")
            return False

    def _smart_text_segmentation(self, text: str) -> list:
        """🧠 智能文本分段 - 避免重复问题"""
        segments = []
        current_segment = {'type': 'text', 'content': ''}

        i = 0
        while i < len(text):
            char = text[i]

            # 检查是否为表情符号
            if self._is_emoji_or_special(char):
                # 保存当前文本段
                if current_segment['content']:
                    segments.append(current_segment)
                    current_segment = {'type': 'text', 'content': ''}

                # 收集连续的表情符号
                emoji_content = ''
                while i < len(text) and self._is_emoji_or_special(text[i]):
                    emoji_content += text[i]
                    i += 1

                # 添加表情符号段
                segments.append({'type': 'emoji', 'content': emoji_content})
                continue
            else:
                current_segment['content'] += char
                i += 1

        # 添加最后的文本段
        if current_segment['content']:
            segments.append(current_segment)

        self.logger.info(f"🧠 智能分段结果: {len(segments)} 个段落")
        return segments

    async def _get_current_input_content(self, driver_wrapper, element):
        """📋 获取当前输入框内容"""
        try:
            content = await driver_wrapper.execute_async(
                lambda: driver_wrapper.driver.execute_script(
                    'return arguments[0].value || arguments[0].textContent || arguments[0].innerText || "";',
                    element
                )
            )
            return content or ""
        except Exception as e:
            self.logger.debug(f"获取输入框内容失败: {e}")
            return ""

    def _validate_emoji_preservation(self, original_text: str, actual_text: str) -> bool:
        """🎭 验证表情符号是否被保留"""
        if not original_text or not actual_text:
            return False

        # 提取原文本中的表情符号
        original_emojis = [char for char in original_text if self._is_emoji_or_special(char)]
        actual_emojis = [char for char in actual_text if self._is_emoji_or_special(char)]

        # 检查表情符号是否保留 - 如果原文有表情符号，实际文本也应该有
        if len(original_emojis) > 0:
            emoji_preserved = len(actual_emojis) >= len(original_emojis) * 0.8  # 至少保留80%
        else:
            emoji_preserved = True  # 原文没有表情符号，不需要验证

        # 检查文本长度是否合理（避免重复）
        length_reasonable = len(actual_text) <= len(original_text) * 1.5

        # 检查是否包含主要内容 - 更宽松的验证，允许换行符差异
        original_clean = original_text.replace('\n', '').replace('\r', '').strip()
        actual_clean = actual_text.replace('\n', '').replace('\r', '').strip()

        # 如果去除换行符后内容基本一致，认为是成功的
        content_preserved = (
            len(actual_clean) >= len(original_clean) * 0.7 or  # 内容保留度
            original_clean in actual_clean or                   # 包含关系
            actual_clean in original_clean                      # 反向包含
        )

        result = emoji_preserved and length_reasonable and content_preserved

        if not result:
            self.logger.warning(f"验证失败 - 表情符号保留: {emoji_preserved}, 长度合理: {length_reasonable}, 内容保留: {content_preserved}")
            self.logger.warning(f"原始表情符号: {original_emojis}, 实际表情符号: {actual_emojis}")
            self.logger.warning(f"原始内容(去换行): '{original_clean}', 实际内容(去换行): '{actual_clean}'")
        else:
            self.logger.info(f"✅ 验证成功 - 表情符号: {len(actual_emojis)}/{len(original_emojis)}, 内容长度: {len(actual_text)}/{len(original_text)}")

        return result

    async def _input_emoji_segment_safely(self, driver_wrapper, element, emoji_content: str):
        """🎭 安全输入表情符号段"""
        try:
            # 优先使用剪贴板方法
            success = await self._input_via_clipboard(driver_wrapper, element, emoji_content)
            if not success:
                # 备用JavaScript方法
                success = await self._input_emoji_via_js(driver_wrapper, element, emoji_content)

            if not success:
                self.logger.warning(f"表情符号段输入失败，跳过: '{emoji_content}'")

        except Exception as e:
            self.logger.warning(f"表情符号段输入异常: {e}")

    async def _input_text_segment_safely(self, driver_wrapper, element, text_content: str):
        """🔤 安全输入文本段"""
        try:
            # 逐字符输入，确保换行符正确处理
            for char in text_content:
                if char == '\n':
                    await driver_wrapper.execute_async(
                        lambda: ActionChains(driver_wrapper.driver)
                        .key_down(Keys.SHIFT)
                        .send_keys(Keys.ENTER)
                        .key_up(Keys.SHIFT)
                        .perform()
                    )
                else:
                    await driver_wrapper.execute_async(
                        lambda: ActionChains(driver_wrapper.driver)
                        .send_keys(char)
                        .perform()
                    )

                # 短暂停顿模拟人类打字
                await asyncio.sleep(random.uniform(0.05, 0.15))

        except Exception as e:
            self.logger.warning(f"文本段输入异常: {e}")

    def _group_emoji_sequences(self, text: str) -> list:
        """🎭 将文本分组，连续表情符号合并为一组"""
        segments = []
        current_segment = {'type': 'text', 'content': ''}

        for char in text:
            is_emoji = self._is_emoji_or_special(char)

            if is_emoji:
                # 如果当前段是文本且有内容，先保存
                if current_segment['type'] == 'text' and current_segment['content']:
                    segments.append(current_segment)
                    current_segment = {'type': 'text', 'content': ''}

                # 如果当前段不是表情符号段，创建新的表情符号段
                if current_segment['type'] != 'emoji':
                    if current_segment['content']:  # 保存之前的文本段
                        segments.append(current_segment)
                    current_segment = {'type': 'emoji', 'content': ''}

                current_segment['content'] += char
            else:
                # 如果当前段是表情符号且有内容，先保存
                if current_segment['type'] == 'emoji' and current_segment['content']:
                    segments.append(current_segment)
                    current_segment = {'type': 'emoji', 'content': ''}

                # 如果当前段不是文本段，创建新的文本段
                if current_segment['type'] != 'text':
                    if current_segment['content']:  # 保存之前的表情符号段
                        segments.append(current_segment)
                    current_segment = {'type': 'text', 'content': ''}

                current_segment['content'] += char

        # 保存最后一段
        if current_segment['content']:
            segments.append(current_segment)

        self.logger.info(f"🎭 文本分组结果: {segments}")
        return segments

    async def _input_text_segment(self, driver_wrapper, element, segment: dict):
        """🔤 输入文本段 - 根据类型选择最佳输入方法"""
        content = segment['content']
        segment_type = segment['type']

        if segment_type == 'emoji':
            # 表情符号段：使用剪贴板方法一次性输入
            self.logger.info(f"🎭 输入表情符号段: '{content}' (长度: {len(content)})")
            success = await self._input_via_clipboard(driver_wrapper, element, content)
            if not success:
                self.logger.warning(f"表情符号段输入失败，尝试JavaScript方法: '{content}'")
                # 备用方案：使用JavaScript方法
                success = await self._input_emoji_via_js(driver_wrapper, element, content)
                if not success:
                    self.logger.warning(f"JavaScript方法也失败，尝试逐个输入: '{content}'")
                    # 最后备用方案：逐个输入，但使用直接输入避免递归
                    for char in content:
                        await self._input_emoji_direct(driver_wrapper, element, char)
                        await asyncio.sleep(0.1)
        else:
            # 文本段：逐个字符输入以保持自然性
            self.logger.info(f"🔤 输入文本段: '{content}' (长度: {len(content)})")
            for char in content:
                await self._input_single_char(driver_wrapper, element, char)
                delay = self._get_human_typing_delay(char)
                await asyncio.sleep(delay)

    async def _input_single_char(self, driver_wrapper, element, char: str):
        """🔤 输入单个字符 - 智能处理表情符号和特殊字符"""
        try:
            # 🎯 检查是否为表情符号或特殊字符
            if self._is_emoji_or_special(char):
                self.logger.debug(f"检测到表情符号: '{char}' (Unicode: U+{ord(char):04X})")
                # 使用多种方法尝试输入表情符号
                success = await self._input_emoji_multiple_methods(driver_wrapper, element, char)
                if not success:
                    self.logger.warning(f"表情符号 '{char}' 输入失败，跳过")
            elif char == '\n':
                # 换行符特殊处理：使用Shift+Enter或Enter
                self.logger.info(f"🔄 输入换行符")
                await driver_wrapper.execute_async(
                    lambda: ActionChains(driver_wrapper.driver)
                    .key_down(Keys.SHIFT)
                    .send_keys(Keys.ENTER)
                    .key_up(Keys.SHIFT)
                    .perform()
                )
            else:
                # 普通字符直接输入
                await driver_wrapper.execute_async(
                    lambda: ActionChains(driver_wrapper.driver)
                    .send_keys(char)
                    .perform()
                )

        except Exception as e:
            self.logger.debug(f"字符 '{char}' 输入失败: {e}")

    async def _input_emoji_direct(self, driver_wrapper, element, emoji: str):
        """🎭 直接输入单个表情符号 - 避免递归调用"""
        try:
            # 尝试使用ActionChains直接发送
            await driver_wrapper.execute_async(
                lambda: ActionChains(driver_wrapper.driver)
                .send_keys(emoji)
                .perform()
            )
            self.logger.debug(f"直接输入表情符号成功: '{emoji}'")
        except Exception as e:
            self.logger.debug(f"直接输入表情符号失败: '{emoji}', 错误: {e}")

    async def _input_emoji_multiple_methods(self, driver_wrapper, element, emoji: str):
        """🎭 使用多种方法输入表情符号"""
        methods = [
            ("剪贴板方法", self._input_via_clipboard),
            ("JavaScript方法", self._input_emoji_via_js),
            ("Unicode方法", self._input_emoji_via_unicode)
        ]

        for method_name, method in methods:
            try:
                self.logger.debug(f"尝试{method_name}输入表情符号: '{emoji}'")
                await method(driver_wrapper, element, emoji)

                # 验证是否输入成功
                await asyncio.sleep(0.1)
                current_value = await driver_wrapper.execute_async(
                    lambda: driver_wrapper.driver.execute_script(
                        "return arguments[0].value || arguments[0].textContent || arguments[0].innerText || '';",
                        element
                    )
                )

                if emoji in current_value:
                    self.logger.debug(f"{method_name}成功输入表情符号: '{emoji}'")
                    return True

            except Exception as e:
                self.logger.debug(f"{method_name}失败: {e}")
                continue

        return False

    def _is_emoji_or_special(self, char: str) -> bool:
        """🎭 检查是否为表情符号或特殊字符"""
        if not char:
            return False

        char_code = ord(char)

        # 表情符号和特殊字符的Unicode范围（更全面）
        special_ranges = [
            (0x1F600, 0x1F64F),  # 表情符号
            (0x1F300, 0x1F5FF),  # 杂项符号和象形文字
            (0x1F680, 0x1F6FF),  # 交通和地图符号
            (0x1F1E0, 0x1F1FF),  # 区域指示符号
            (0x2600, 0x26FF),    # 杂项符号
            (0x2700, 0x27BF),    # 装饰符号
            (0x1F900, 0x1F9FF),  # 补充符号和象形文字
            (0x1F018, 0x1F270),  # 各种符号
            (0x238C, 0x2454),    # 杂项技术符号
            (0x20D0, 0x20FF),    # 组合变音符号
        ]

        # 特殊检测：花朵符号🌸 (U+1F338)
        if char == '🌸':
            self.logger.debug(f"检测到花朵表情符号: '{char}' (U+{char_code:04X})")
            return True

        # 检查是否在特殊字符范围内，或者超出BMP范围
        is_special = (char_code > 0xFFFF or
                     any(start <= char_code <= end for start, end in special_ranges))

        if is_special:
            self.logger.debug(f"检测到表情符号/特殊字符: '{char}' (U+{char_code:04X})")

        return is_special

    async def _input_via_clipboard(self, driver_wrapper, element, text: str):
        """📋 通过剪贴板输入文本 - 完美支持表情符号"""
        try:
            import pyperclip

            # 保存当前剪贴板内容
            original_clipboard = ""
            try:
                original_clipboard = pyperclip.paste()
            except:
                pass

            # 将文本复制到剪贴板
            pyperclip.copy(text)
            await asyncio.sleep(0.1)  # 增加等待时间确保复制成功

            # 确保元素有焦点
            await driver_wrapper.execute_async(element.click)
            await asyncio.sleep(0.05)

            # 模拟Ctrl+V粘贴
            await driver_wrapper.execute_async(
                lambda: ActionChains(driver_wrapper.driver)
                .key_down(Keys.CONTROL)
                .send_keys('v')
                .key_up(Keys.CONTROL)
                .perform()
            )

            await asyncio.sleep(0.2)  # 等待粘贴完成

            # 验证输入是否成功
            current_value = await driver_wrapper.execute_async(
                lambda: driver_wrapper.driver.execute_script(
                    "return arguments[0].value || arguments[0].textContent || arguments[0].innerText || '';",
                    element
                )
            )

            # 恢复原剪贴板内容
            if original_clipboard:
                pyperclip.copy(original_clipboard)

            # 使用改进的验证逻辑
            if current_value and self._validate_emoji_preservation(text, current_value):
                self.logger.info(f"✅ 剪贴板输入成功: '{text}'")
                return True
            else:
                self.logger.warning(f"❌ 剪贴板输入验证失败: 期望'{text}', 实际'{current_value}'")
                return False

        except Exception as e:
            self.logger.warning(f"剪贴板输入失败: {e}")
            return False

    async def _input_emoji_via_js(self, driver_wrapper, element, emoji: str):
        """🎭 通过JavaScript输入表情符号 - 增强版本，确保无头模式兼容性"""
        try:
            result = await driver_wrapper.execute_async(
                lambda: driver_wrapper.driver.execute_script("""
                    const element = arguments[0];
                    const emoji = arguments[1];

                    // 确保元素获得焦点
                    element.focus();

                    // 获取当前光标位置
                    const start = element.selectionStart || 0;
                    const end = element.selectionEnd || 0;
                    const currentValue = element.value || element.textContent || element.innerText || '';

                    // 在光标位置插入表情符号
                    const newValue = currentValue.slice(0, start) + emoji + currentValue.slice(end);

                    // 设置新值 - 多种方式确保兼容性
                    if (element.tagName.toLowerCase() === 'textarea' || element.tagName.toLowerCase() === 'input') {
                        element.value = newValue;
                    } else {
                        element.textContent = newValue;
                        element.innerText = newValue;
                    }

                    // 触发完整的事件序列 - 确保无头模式下正确识别
                    const events = [
                        new Event('focus', { bubbles: true }),
                        new Event('keydown', { bubbles: true }),
                        new InputEvent('input', {
                            bubbles: true,
                            cancelable: true,
                            inputType: 'insertText',
                            data: emoji
                        }),
                        new Event('keyup', { bubbles: true }),
                        new Event('change', { bubbles: true })
                    ];

                    // 依次触发所有事件
                    events.forEach(event => {
                        try {
                            element.dispatchEvent(event);
                        } catch (e) {
                            console.log('Event dispatch failed:', e);
                        }
                    });

                    // 更新光标位置
                    const newPosition = start + emoji.length;
                    if (element.setSelectionRange) {
                        element.setSelectionRange(newPosition, newPosition);
                    }

                    // 强制DOM更新 - 对无头模式很重要
                    element.blur();
                    element.focus();

                    // 返回最终值进行验证
                    return element.value || element.textContent || element.innerText || '';
                """, element, emoji)
            )

            # 等待DOM更新完成
            await asyncio.sleep(0.2)

            # 验证输入是否成功
            if result and emoji in result:
                self.logger.info(f"✅ JavaScript输入成功: '{emoji}'")
                return True
            else:
                self.logger.warning(f"❌ JavaScript输入验证失败: 期望'{emoji}', 实际'{result}'")
                return False

        except Exception as e:
            self.logger.warning(f"JavaScript表情符号输入失败: {e}")
            return False

    async def _input_emoji_via_unicode(self, driver_wrapper, element, emoji: str):
        """🔤 通过Unicode码点输入表情符号"""
        try:
            # 获取表情符号的Unicode码点
            unicode_code = f"U+{ord(emoji):04X}"
            self.logger.debug(f"尝试Unicode输入: {emoji} ({unicode_code})")

            # 使用Alt+数字键输入Unicode
            # 注意：这种方法在某些系统上可能不工作
            await driver_wrapper.execute_async(
                lambda: ActionChains(driver_wrapper.driver)
                .key_down(Keys.ALT)
                .send_keys(f"+{ord(emoji):04x}")
                .key_up(Keys.ALT)
                .perform()
            )

        except Exception as e:
            self.logger.warning(f"Unicode表情符号输入失败: {e}")
            raise

    def _get_human_typing_delay(self, char: str) -> float:
        """🕐 获取人类打字延迟"""
        base_delay = random.uniform(0.05, 0.20)  # 基础延迟

        # 根据字符类型调整延迟
        if char == ' ':
            return base_delay * random.uniform(1.2, 2.0)  # 空格稍慢
        elif char in '.,!?;:\n':
            return base_delay * random.uniform(1.5, 2.5)  # 标点符号更慢
        elif self._is_emoji_or_special(char):
            return base_delay * random.uniform(2.0, 3.5)  # 表情符号最慢
        else:
            return base_delay

    async def _post_input_natural_behavior(self, driver_wrapper, element):
        """🎭 输入完成后的自然行为"""
        try:
            # 模拟用户检查输入内容
            await asyncio.sleep(random.uniform(0.3, 0.8))

            # 模拟按End键移动到文本末尾
            await driver_wrapper.execute_async(
                lambda: ActionChains(driver_wrapper.driver)
                .send_keys(Keys.END)
                .perform()
            )

            # 短暂停顿，模拟用户满意地查看输入结果
            await asyncio.sleep(random.uniform(0.5, 1.2))

        except Exception as e:
            self.logger.debug(f"输入后自然行为模拟失败: {e}")

    async def handle_x_platform_masks(self, driver_wrapper) -> bool:
        """
        处理X平台特有的遮罩层和弹窗
        
        Args:
            driver_wrapper: WebDriver包装器
            
        Returns:
            是否成功处理遮罩层
        """
        try:
            self.logger.info("开始检测和处理X平台遮罩层...")
            
            # 🎯 X平台2024年最新遮罩层选择器 - 基于实际错误日志
            mask_selectors = [
                # 主要遮罩层
                '[data-testid="mask"]',
                '[data-testid="sheetDialog"]',
                '[data-testid="confirmationSheetDialog"]',
                '[role="dialog"]',
                '[aria-modal="true"]',
                '[data-testid="app-bar-close"]',
                '[data-testid="closeButton"]',

                # 通用遮罩层
                '.modal-mask',
                '.overlay',
                '.popup-mask',

                # 🆕 基于错误日志的精确遮罩层选择器
                'div.css-175oi2r.r-1p0dtai.r-1d2f490.r-1xcajam.r-zchlnj.r-ipm5af.r-1ffj0ar[data-testid="mask"]',
                'div.css-175oi2r.r-1p0dtai.r-1d2f490.r-1xcajam.r-zchlnj.r-ipm5af.r-1ffj0ar',

                # 固定定位的遮罩层
                'div[style*="position: fixed"][style*="z-index"]',
                'div[class*="css-175oi2r"][style*="position: fixed"]',

                # 🎯 精确的阻挡点击元素 - 避免匹配过多普通元素
                'div[style*="pointer-events: auto"][style*="position: fixed"][style*="z-index"]',
                # ⚠️ 只匹配真正的遮罩层，不是所有带这些类的元素
                'div[class*="r-1p0dtai"][class*="r-1d2f490"][data-testid="mask"]',
                'div[class*="r-1p0dtai"][class*="r-1d2f490"][style*="position: fixed"]'
            ]
            
            masks_handled = 0
            
            for selector in mask_selectors:
                try:
                    # 检查是否存在遮罩层
                    elements = await driver_wrapper.find_elements(By.CSS_SELECTOR, selector)
                    mask_count = len(elements)
                    
                    if mask_count > 0:
                        self.logger.info(f"发现遮罩层: {selector} (数量: {mask_count})")
                        
                        # 尝试多种方式关闭遮罩层
                        success = await self._close_mask_element(driver_wrapper, selector)
                        
                        if success:
                            masks_handled += 1
                            self.logger.info(f"成功关闭遮罩层: {selector}")
                            
                            # 等待遮罩层消失
                            await asyncio.sleep(0.5)
                        
                except Exception as e:
                    self.logger.warning(f"处理遮罩层 {selector} 时出错: {e}")
                    continue
            
            # 🚀 强力清除：JavaScript直接移除遮罩层
            if masks_handled > 0:
                try:
                    self.logger.info("🔥 执行强力遮罩层清除...")
                    await driver_wrapper.execute_async(lambda: driver_wrapper.driver.execute_script("""
                        // 移除所有可能的遮罩层
                        const maskSelectors = [
                            '[data-testid="mask"]',
                            '[role="dialog"]',
                            '[aria-modal="true"]',
                            'div[class*="css-175oi2r"][class*="r-1p0dtai"]',
                            'div[style*="position: fixed"][style*="z-index"]'
                        ];

                        let removed = 0;
                        maskSelectors.forEach(selector => {
                            const elements = document.querySelectorAll(selector);
                            elements.forEach(el => {
                                // 检查是否是遮罩层（通常有高z-index和固定定位）
                                const style = window.getComputedStyle(el);
                                if (style.position === 'fixed' || style.zIndex > 1000) {
                                    el.remove();
                                    removed++;
                                }
                            });
                        });

                        return removed;
                    """))
                    await asyncio.sleep(0.5)
                except Exception as e:
                    self.logger.warning(f"强力清除失败: {e}")

            # 额外处理：按ESC键关闭可能的弹窗
            try:
                await driver_wrapper.execute_async(
                    lambda: ActionChains(driver_wrapper.driver).send_keys(Keys.ESCAPE).perform()
                )
                await asyncio.sleep(0.3)
            except Exception as e:
                self.logger.warning(f"按ESC键失败: {e}")

            self.logger.info(f"遮罩层处理完成，共处理 {masks_handled} 个遮罩层")
            return masks_handled > 0
            
        except Exception as e:
            self.logger.error(f"处理X平台遮罩层失败: {e}")
            return False
    
    async def _close_mask_element(self, driver_wrapper, selector: str) -> bool:
        """
        关闭指定的遮罩层元素
        
        Args:
            driver_wrapper: WebDriver包装器
            selector: 遮罩层选择器
            
        Returns:
            是否成功关闭
        """
        try:
            elements = await driver_wrapper.find_elements(By.CSS_SELECTOR, selector)
            element_count = len(elements)
            
            if element_count == 0:
                return False
            
            # 尝试多种关闭方式
            close_methods = [
                # 方法1: 查找关闭按钮
                self._find_and_click_close_button,
                # 方法2: 点击遮罩层本身
                self._click_mask_to_close,
                # 方法3: 使用JavaScript移除
                self._remove_mask_with_js
            ]
            
            for method in close_methods:
                try:
                    success = await method(driver_wrapper, selector)
                    if success:
                        return True
                except Exception as e:
                    self.logger.warning(f"关闭方法失败: {e}")
                    continue
            
            return False
            
        except Exception as e:
            self.logger.warning(f"关闭遮罩层元素失败: {e}")
            return False
    
    async def _find_and_click_close_button(self, driver_wrapper, mask_selector: str) -> bool:
        """
        查找并点击关闭按钮
        """
        try:
            # 在遮罩层内查找关闭按钮
            close_selectors = [
                f'{mask_selector} [data-testid="app-bar-close"]',
                f'{mask_selector} [data-testid="closeButton"]',
                f'{mask_selector} [aria-label*="Close"]',
                f'{mask_selector} [aria-label*="关闭"]',
                f'{mask_selector} button[aria-label*="Close"]',
                f'{mask_selector} .close-button',
                f'{mask_selector} .modal-close'
            ]

            for close_selector in close_selectors:
                try:
                    elements = await driver_wrapper.find_elements(By.CSS_SELECTOR, close_selector)
                    if elements:
                        close_button = elements[0]
                        await driver_wrapper.execute_async(close_button.click)
                        await asyncio.sleep(0.3)
                        return True
                except Exception:
                    continue

            return False

        except Exception as e:
            self.logger.warning(f"查找关闭按钮失败: {e}")
            return False
    
    async def _click_mask_to_close(self, driver_wrapper, mask_selector: str) -> bool:
        """
        点击遮罩层本身来关闭
        """
        try:
            elements = await driver_wrapper.find_elements(By.CSS_SELECTOR, mask_selector)
            if elements:
                mask = elements[0]
                # 获取遮罩层位置和大小
                location = await driver_wrapper.execute_async(lambda: mask.location)
                size = await driver_wrapper.execute_async(lambda: mask.size)

                # 点击遮罩层的边缘区域
                click_x = location['x'] + 10
                click_y = location['y'] + 10

                await driver_wrapper.execute_async(
                    lambda: ActionChains(driver_wrapper.driver).move_to_element_with_offset(
                        mask, 10, 10
                    ).click().perform()
                )
                await asyncio.sleep(0.3)
                return True

            return False

        except Exception as e:
            self.logger.warning(f"点击遮罩层关闭失败: {e}")
            return False
    
    async def _remove_mask_with_js(self, driver_wrapper, mask_selector: str) -> bool:
        """
        使用JavaScript直接移除遮罩层
        """
        try:
            js_code = f"""
            const masks = document.querySelectorAll('{mask_selector}');
            let removed = 0;
            masks.forEach(mask => {{
                if (mask && mask.parentNode) {{
                    mask.parentNode.removeChild(mask);
                    removed++;
                }}
            }});
            return removed;
            """

            removed_count = await driver_wrapper.execute_script(js_code)
            return removed_count > 0

        except Exception as e:
            self.logger.warning(f"JavaScript移除遮罩层失败: {e}")
            return False
    
    async def enhanced_safe_click(self, driver_wrapper, selector: str, max_retries: int = 3) -> bool:
        """
        增强的安全点击，包含遮罩层处理和重试机制

        Args:
            driver_wrapper: WebDriver包装器
            selector: 元素选择器
            max_retries: 最大重试次数

        Returns:
            是否成功点击
        """
        for attempt in range(max_retries):
            try:
                self.logger.info(f"尝试点击元素 {selector} (第 {attempt + 1} 次)")
                
                # 首先处理可能的遮罩层
                await self.handle_x_platform_masks(driver_wrapper)

                # 等待元素可见且可用
                wait = WebDriverWait(driver_wrapper.driver, 10)
                element = await driver_wrapper.execute_async(
                    lambda: wait.until(EC.element_to_be_clickable((By.CSS_SELECTOR, selector)))
                )

                # 检查元素是否被禁用
                is_disabled = await driver_wrapper.execute_async(
                    lambda: element.get_attribute('disabled')
                )
                aria_disabled = await driver_wrapper.execute_async(
                    lambda: element.get_attribute('aria-disabled')
                )

                if is_disabled == 'true' or aria_disabled == 'true':
                    self.logger.warning(f"元素被禁用，等待启用...")
                    await asyncio.sleep(1)
                    continue

                # 模拟鼠标移动到元素
                await driver_wrapper.execute_async(
                    lambda: ActionChains(driver_wrapper.driver).move_to_element(element).perform()
                )
                await asyncio.sleep(random.uniform(0.1, 0.3))
                
                # 尝试多种点击方式
                click_methods = [
                    lambda: driver_wrapper.execute_async(element.click),
                    lambda: driver_wrapper.execute_async(
                        lambda: ActionChains(driver_wrapper.driver).click(element).perform()
                    ),
                    lambda: driver_wrapper.execute_script(f'document.querySelector("{selector}").click()')
                ]

                for i, click_method in enumerate(click_methods):
                    try:
                        await click_method()
                        self.logger.info(f"成功点击元素 {selector} (方式 {i + 1})")

                        # 随机延迟
                        await self.behavior_simulator.random_delay()
                        return True

                    except Exception as click_error:
                        self.logger.warning(f"点击方式 {i + 1} 失败: {click_error}")
                        if i < len(click_methods) - 1:
                            await asyncio.sleep(0.5)
                        continue

                # 如果所有点击方式都失败，再次处理遮罩层
                if attempt < max_retries - 1:
                    self.logger.warning(f"点击失败，重新处理遮罩层...")
                    await self.handle_x_platform_masks(driver_wrapper)
                    await asyncio.sleep(1)
                
            except Exception as e:
                self.logger.warning(f"点击尝试 {attempt + 1} 失败: {e}")
                if attempt < max_retries - 1:
                    await asyncio.sleep(1)
                continue
        
        self.logger.error(f"所有点击尝试都失败: {selector}")
        return False
