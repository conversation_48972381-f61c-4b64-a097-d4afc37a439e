#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
稳定版主程序入口
集成所有稳定性改进
"""

import sys
import os
import asyncio
import signal
import atexit
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def setup_stable_environment():
    """设置稳定运行环境"""
    print("🚀 初始化稳定运行环境...")
    
    try:
        # 1. 设置日志系统
        from src.config.logging_config import setup_logging, setup_error_logging
        setup_logging()
        setup_error_logging()
        print("  ✅ 日志系统已配置")
        
        # 2. 设置全局异常处理
        from src.utils.exception_handler import setup_exception_handling
        setup_exception_handling()
        print("  ✅ 全局异常处理已设置")
        
        # 3. 设置信号处理
        from src.utils.signal_handler import setup_signal_handling, add_cleanup_function
        setup_signal_handling()
        print("  ✅ 信号处理已设置")
        
        # 4. 启动系统监控
        from src.core.system_monitor import get_system_monitor
        monitor = get_system_monitor()
        monitor.start_monitoring(interval=300)  # 每5分钟检查一次
        add_cleanup_function(monitor.stop_monitoring)
        print("  ✅ 系统监控已启动")
        
        # 5. 初始化数据库
        from src.database.connection import init_db_manager
        from src.config.settings import get_settings
        
        settings = get_settings()
        init_db_manager(settings.database_url)
        print("  ✅ 数据库已初始化")
        
        # 6. 初始化浏览器池
        from src.core.browser_manager import BrowserPool
        browser_pool = BrowserPool(max_browsers=settings.max_browsers)
        
        # 注册清理函数
        add_cleanup_function(lambda: browser_pool.sync_close_all(is_final_shutdown=True))
        print("  ✅ 浏览器池已初始化")
        
        print("🎉 稳定运行环境初始化完成！")
        return browser_pool, settings
        
    except Exception as e:
        print(f"❌ 环境初始化失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

async def run_stable_system():
    """运行稳定系统"""
    print("🔄 启动稳定系统...")
    
    try:
        # 初始化环境
        browser_pool, settings = setup_stable_environment()
        
        # 等待浏览器池初始化完成
        await browser_pool.initialize()
        
        print("✅ 系统已启动，进入稳定运行模式...")
        print("📊 系统状态:")
        print(f"  - 无头模式: {settings.browser_headless}")
        print(f"  - 最大浏览器数: {settings.max_browsers}")
        print(f"  - 浏览器超时: {settings.browser_timeout}ms")
        print(f"  - 数据库: {settings.database_url}")
        
        # 运行示例任务
        await run_example_tasks(browser_pool)
        
        # 保持运行状态
        print("🔄 系统进入持续运行状态...")
        print("按 Ctrl+C 优雅退出")
        
        try:
            while True:
                await asyncio.sleep(60)  # 每分钟检查一次
                
                # 检查系统状态
                active_drivers = len(browser_pool.drivers)
                if active_drivers > 0:
                    print(f"📊 当前活跃浏览器: {active_drivers}个")
                
        except KeyboardInterrupt:
            print("\n🛑 收到退出信号，开始优雅关闭...")
            
    except Exception as e:
        print(f"❌ 系统运行异常: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        print("🧹 系统清理完成")

async def run_example_tasks(browser_pool):
    """运行示例任务"""
    print("🧪 运行示例任务...")
    
    try:
        from src.core.account_manager import AccountManager
        
        # 获取账号
        account_manager = AccountManager()
        accounts, _ = account_manager.get_accounts(page_size=3)
        
        if not accounts:
            print("  ⚠️ 没有找到账号，跳过示例任务")
            return
        
        print(f"  📱 找到 {len(accounts)} 个账号")
        
        # 测试浏览器启动
        for i, account in enumerate(accounts[:2]):  # 只测试前2个账号
            try:
                print(f"  🚀 测试账号 {i+1}: {account.username}")
                
                # 获取浏览器
                driver = await browser_pool.get_driver(account)
                
                if driver:
                    # 简单导航测试
                    await driver.get("https://www.google.com")
                    title = await driver.execute_async(lambda: driver.driver.title)
                    print(f"    ✅ 成功访问: {title}")
                    
                    # 释放浏览器
                    await browser_pool._cleanup_account_driver(account.id)
                    print(f"    ✅ 浏览器已释放")
                else:
                    print(f"    ❌ 浏览器启动失败")
                
            except Exception as e:
                print(f"    ❌ 测试失败: {e}")
        
        print("  ✅ 示例任务完成")
        
    except Exception as e:
        print(f"  ❌ 示例任务异常: {e}")

def main():
    """主函数"""
    print("🎯 启动7×24小时稳定运行系统")
    print("="*50)
    
    try:
        # 检查Python版本
        if sys.version_info < (3, 7):
            print("❌ 需要Python 3.7或更高版本")
            sys.exit(1)
        
        # 运行系统
        asyncio.run(run_stable_system())
        
    except KeyboardInterrupt:
        print("\n👋 程序已退出")
    except Exception as e:
        print(f"❌ 程序异常: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
