#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
信号处理器
"""

import signal
import sys
import atexit
from loguru import logger

_cleanup_functions = []

def add_cleanup_function(func):
    """添加清理函数"""
    _cleanup_functions.append(func)

def signal_handler(signum, frame):
    """信号处理器"""
    logger.info(f"收到信号 {signum}，开始优雅关闭...")
    
    # 执行清理函数
    for cleanup_func in _cleanup_functions:
        try:
            cleanup_func()
        except Exception as e:
            logger.error(f"清理函数执行失败: {e}")
    
    logger.info("程序已优雅关闭")
    sys.exit(0)

def setup_signal_handling():
    """设置信号处理"""
    # 注册信号处理器
    signal.signal(signal.SIGTERM, signal_handler)
    signal.signal(signal.SIGINT, signal_handler)
    
    # 注册退出处理器
    atexit.register(cleanup_on_exit)
    
    logger.info("📡 信号处理器已设置")

def cleanup_on_exit():
    """退出时清理"""
    logger.info("程序正在退出，执行清理...")
    
    # 执行清理函数
    for cleanup_func in _cleanup_functions:
        try:
            cleanup_func()
        except Exception as e:
            logger.error(f"退出清理失败: {e}")
