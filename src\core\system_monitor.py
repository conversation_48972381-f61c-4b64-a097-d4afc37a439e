#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统监控器
"""

import time
import psutil
import threading
from datetime import datetime
from loguru import logger

class SystemMonitor:
    """系统监控器"""
    
    def __init__(self):
        self.monitoring = False
        self.monitor_thread = None
        self.thresholds = {
            'memory': 80,  # 内存使用率阈值
            'disk': 90,    # 磁盘使用率阈值
            'cpu': 90      # CPU使用率阈值
        }
        
    def start_monitoring(self, interval=60):
        """开始监控"""
        if self.monitoring:
            return
            
        self.monitoring = True
        self.monitor_thread = threading.Thread(
            target=self._monitor_loop,
            args=(interval,),
            daemon=True
        )
        self.monitor_thread.start()
        logger.info("📊 系统监控已启动")
    
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        logger.info("📊 系统监控已停止")
    
    def _monitor_loop(self, interval):
        """监控循环"""
        while self.monitoring:
            try:
                self._check_system_resources()
                time.sleep(interval)
            except Exception as e:
                logger.error(f"系统监控异常: {e}")
                time.sleep(10)
    
    def _check_system_resources(self):
        """检查系统资源"""
        # 检查内存
        memory = psutil.virtual_memory()
        if memory.percent > self.thresholds['memory']:
            logger.warning(f"内存使用率过高: {memory.percent:.1f}%")
        
        # 检查磁盘
        disk = psutil.disk_usage('/')
        disk_percent = (disk.used / disk.total) * 100
        if disk_percent > self.thresholds['disk']:
            logger.warning(f"磁盘使用率过高: {disk_percent:.1f}%")
        
        # 检查CPU
        cpu_percent = psutil.cpu_percent(interval=1)
        if cpu_percent > self.thresholds['cpu']:
            logger.warning(f"CPU使用率过高: {cpu_percent:.1f}%")

# 全局监控器实例
_system_monitor = None

def get_system_monitor():
    """获取系统监控器"""
    global _system_monitor
    if _system_monitor is None:
        _system_monitor = SystemMonitor()
    return _system_monitor
