#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
应用程序配置管理
"""

import os
from pathlib import Path
from typing import Dict, Any
from pydantic import field_validator
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """应用程序设置"""
    
    # 应用设置
    app_name: str = "X自动发帖程序"
    app_version: str = "1.0.0"
    debug: bool = False
    
    # 数据库设置
    database_url: str = "sqlite:///data/app.db"
    redis_url: str = "redis://localhost:6379/0"
    
    # 浏览器设置 - 有头模式，便于调试和观察
    browser_headless: bool = True
    browser_timeout: int = 30000
    max_browsers: int = 50
    browser_user_data_dir: str = "data/browser_profiles"  # 相对于程序根目录

    # Chrome进程管理设置
    chrome_safe_cleanup: bool = True  # 启用安全清理模式，保护用户正常使用的浏览器
    chrome_cleanup_warning: bool = True  # 强制清理前显示警告
    
    # 任务设置
    max_concurrent_tasks: int = 10
    task_retry_count: int = 3
    keep_alive_interval: int = 3600
    
    # 日志设置
    log_level: str = "INFO"
    log_file: str = "data/logs/app.log"
    log_max_size: int = 10485760  # 10MB
    log_backup_count: int = 5
    
    # 速率限制设置
    rate_limits: Dict[str, Dict[str, int]] = {
        'post': {'count': 10, 'window': 3600},      # 每小时10条
        'like': {'count': 50, 'window': 3600},      # 每小时50个赞
        'retweet': {'count': 20, 'window': 3600},   # 每小时20个转发
        'comment': {'count': 15, 'window': 3600},   # 每小时15条评论
        'follow': {'count': 20, 'window': 86400}    # 每天20个关注
    }
    
    # 防封设置
    anti_detection: Dict[str, Any] = {
        'random_delay_min': 1.0,
        'random_delay_max': 3.0,
        'mouse_movement_steps': 20,
        'typing_delay_min': 50,
        'typing_delay_max': 150,
        'scroll_pause_min': 0.5,
        'scroll_pause_max': 2.0
    }
    
    # 代理设置
    proxy_test_timeout: int = 10
    proxy_test_url: str = "https://httpbin.org/ip"
    
    # 媒体设置
    media_max_size: int = 50 * 1024 * 1024  # 50MB
    supported_image_formats: list = ['.jpg', '.jpeg', '.png', '.gif', '.webp']
    supported_video_formats: list = ['.mp4', '.mov', '.avi', '.mkv']
    
    @field_validator('database_url')
    @classmethod
    def validate_database_url(cls, v):
        """验证数据库URL"""
        if v.startswith('sqlite:///'):
            # 确保SQLite数据库目录存在
            db_path = v.replace('sqlite:///', '')
            db_dir = Path(db_path).parent
            db_dir.mkdir(parents=True, exist_ok=True)
        return v

    @field_validator('log_file')
    @classmethod
    def validate_log_file(cls, v):
        """验证日志文件路径"""
        log_dir = Path(v).parent
        log_dir.mkdir(parents=True, exist_ok=True)
        return v

    @field_validator('browser_user_data_dir')
    @classmethod
    def validate_browser_dir(cls, v):
        """验证浏览器数据目录"""
        Path(v).mkdir(parents=True, exist_ok=True)
        return v

    @field_validator('log_level')
    @classmethod
    def validate_log_level(cls, v):
        """验证日志级别"""
        allowed_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
        if v.upper() not in allowed_levels:
            raise ValueError(f"日志级别必须是: {', '.join(allowed_levels)}")
        return v.upper()

    @field_validator('max_browsers')
    @classmethod
    def validate_max_browsers(cls, v):
        """验证最大浏览器数量"""
        if v < 1 or v > 100:
            raise ValueError("最大浏览器数量必须在1-100之间")
        return v

    @field_validator('max_concurrent_tasks')
    @classmethod
    def validate_max_concurrent_tasks(cls, v):
        """验证最大并发任务数"""
        if v < 1 or v > 50:
            raise ValueError("最大并发任务数必须在1-50之间")
        return v
    
    model_config = {
        "env_file": ".env",
        "env_file_encoding": "utf-8",
        "case_sensitive": False,
        "env_prefix": "",
        "extra": "allow"
    }


# 全局设置实例
_settings = None


def get_settings() -> Settings:
    """获取全局设置实例"""
    global _settings
    if _settings is None:
        _settings = Settings()
    return _settings


def reload_settings():
    """重新加载设置"""
    global _settings
    _settings = Settings()
    return _settings
