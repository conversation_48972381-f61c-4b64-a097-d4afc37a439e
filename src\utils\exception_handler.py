#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全局异常处理器
"""

import sys
import traceback
from loguru import logger

def global_exception_handler(exc_type, exc_value, exc_traceback):
    """全局异常处理器"""
    if issubclass(exc_type, KeyboardInterrupt):
        # 允许Ctrl+C正常退出
        sys.__excepthook__(exc_type, exc_value, exc_traceback)
        return
    
    # 记录异常
    logger.error(
        "未捕获的异常",
        exc_info=(exc_type, exc_value, exc_traceback)
    )
    
    # 尝试优雅关闭
    try:
        from src.core.browser_manager import BrowserPool
        # 如果有浏览器池实例，尝试关闭
        pass
    except:
        pass

def setup_exception_handling():
    """设置异常处理"""
    sys.excepthook = global_exception_handler
    logger.info("🛡️ 全局异常处理器已设置")

def handle_unhandled_exception(exc_type, exc_value, exc_traceback):
    """处理未处理的异常"""
    logger.error(
        "程序异常退出",
        exc_info=(exc_type, exc_value, exc_traceback)
    )
