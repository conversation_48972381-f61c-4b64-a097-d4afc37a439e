{"accessibility": {"captions": {"soda_registered_language_packs": ["cmn-Hans-CN"]}}, "autofill": {"ablation_seed": "5rXLeta8DUA="}, "breadcrumbs": {"enabled": false, "enabled_time": "13398687976086600"}, "browser": {"shortcut_migration_version": "138.0.7204.184", "whats_new": {"enabled_order": ["PdfSearchify"]}}, "hardware_acceleration_mode_previous": true, "legacy": {"profile": {"name": {"migrated": true}}}, "local": {"password_hash_data_list": []}, "management": {"platform": {"azure_active_directory": 0, "enterprise_mdm_win": 0}}, "network_time": {"network_time_mapping": {"local": **********680.683, "network": **********531.0, "ticks": 76710885413.0, "uncertainty": 11134916.0}}, "optimization_guide": {"model_cache_key_mapping": {"1563922A0C010C80A5": "4F40902F3B6AE19A", "2063922A0C010C80A5": "4F40902F3B6AE19A", "2563922A0C010C80A5": "4F40902F3B6AE19A", "263922A0C010C80A5": "4F40902F3B6AE19A", "2663922A0C010C80A5": "4F40902F3B6AE19A", "4563922A0C010C80A5": "4F40902F3B6AE19A", "963922A0C010C80A5": "4F40902F3B6AE19A"}, "model_execution": {"last_usage_by_feature": {}}, "model_store_metadata": {"15": {"4F40902F3B6AE19A": {"et": "13401280159437753", "kbvd": true, "mbd": "15\\63922A0C010C80A5\\D35DEFE881A1DB4D", "v": "5"}}, "2": {"4F40902F3B6AE19A": {"et": "13401280157536591", "kbvd": true, "mbd": "2\\63922A0C010C80A5\\F7CDBEB14E0C2DDB", "v": "1679317318"}}, "20": {"4F40902F3B6AE19A": {"et": "13401281103014973", "kbvd": false, "mbd": "20\\63922A0C010C80A5\\AB188FD717DD372A", "v": "1745311339"}}, "25": {"4F40902F3B6AE19A": {"et": "13401281103015659", "kbvd": false, "mbd": "25\\63922A0C010C80A5\\8C53DC336DD235AE", "v": "1751987362"}}, "26": {"4F40902F3B6AE19A": {"et": "13410785103015797", "kbvd": false, "mbd": "26\\63922A0C010C80A5\\5C68F71E70980EF4", "v": "1696268326"}}, "45": {"4F40902F3B6AE19A": {"et": "13401281103016193", "kbvd": false, "mbd": "45\\63922A0C010C80A5\\F60A7287288329A3", "v": "240731042075"}}, "9": {"4F40902F3B6AE19A": {"et": "13401281103014325", "kbvd": false, "mbd": "9\\63922A0C010C80A5\\6EC2CB43E5F17499", "v": "1745312779"}}}, "on_device": {"last_version": "138.0.7204.184", "model_crash_count": 0}, "store_file_paths_to_delete": {}}, "os_crypt": {"audit_enabled": true, "encrypted_key": "RFBBUEkBAAAA0Iyd3wEV0RGMegDAT8KX6wEAAADsk/j3VrDrTquu6bbvu785EAAAABwAAABHAG8AbwBnAGwAZQAgAEMAaAByAG8AbQBlAAAAEGYAAAABAAAgAAAAIKmWJpcaAYGc9qA3odhx7WdVe7gsyjbQo6sQBudZHxoAAAAADoAAAAACAAAgAAAA+tBBTLfZ5ROsjijMJ3zXCBKhWD4zeOxOq5ymsQf47r4wAAAAeD4Qrr9Vkv4MRG1yw+WGmDKJI4QTlmPYQQLgsWoZVY2Bxo8rWcpWkxNuH635ysTVQAAAANTVrSLp3LwhtDZcLC8tReuSCHzOhK6kvIlhrPMemP9NedJrzUZnSP1b4gLdiD7yLD02hR4eJaGqyzTpIqoJOwo="}, "os_update_handler_enabled": true, "performance_intervention": {"last_daily_sample": "13398687976162336"}, "policy": {"last_statistics_update": "*****************"}, "privacy_budget": {"meta_experiment_activation_salt": 0.****************}, "profile": {"info_cache": {"Default": {"active_time": **********.159905, "avatar_icon": "chrome://theme/IDR_PROFILE_AVATAR_26", "background_apps": false, "default_avatar_fill_color": -2890755, "default_avatar_stroke_color": -********, "enterprise_label": "", "force_signin_profile_locked": false, "gaia_given_name": "", "gaia_id": "", "gaia_name": "", "hosted_domain": "", "is_consented_primary_account": false, "is_ephemeral": false, "is_glic_eligible": false, "is_using_default_avatar": true, "is_using_default_name": true, "managed_user_id": "", "metrics_bucket_index": 1, "name": "您的 Chrome", "profile_color_seed": -********, "profile_highlight_color": -2890755, "signin.with_credential_provider": false, "user_name": ""}}, "last_active_profiles": [], "metrics": {"next_bucket_index": 2}, "profile_counts_reported": "*****************", "profiles_order": ["<PERSON><PERSON><PERSON>"]}, "profile_network_context_service": {"http_cache_finch_experiment_groups": "None None None None"}, "session_id_generator_last_value": "**********", "signin": {"active_accounts_last_emitted": "*****************"}, "subresource_filter": {"ruleset_version": {"checksum": 0, "content": "", "format": 0}}, "tab_stats": {"discards_external": 0, "discards_frozen": 0, "discards_proactive": 0, "discards_suggested": 0, "discards_urgent": 0, "last_daily_sample": "*****************", "max_tabs_per_window": 1, "reloads_external": 0, "reloads_frozen": 0, "reloads_proactive": 0, "reloads_suggested": 0, "reloads_urgent": 0, "total_tab_count_max": 1, "window_count_max": 1}, "ukm": {"persisted_logs": []}, "uninstall_metrics": {"installation_date2": "**********"}, "user_experience_metrics": {"client_id2": "4957c749-4cb5-415d-92ef-e516e011494c", "client_id_timestamp": "**********", "initial_logs2": [{"data": "H4sIAAAAAAAAAHWTf2wTZRjH3/e2lvbY2dvwsBTQ1+qkm73jvev11k4Xd123pWVXl/1goLDZtefWsbWjLXPDOCpsbplMlsXA3IJGIGFglAlBjBIhyv4ZKAsE/xgTyTBuGiSAMzpDnFljE/7x+eN5Ps83T/LkmzyPfrxg2/Itt6sRrTV16XQjn35wQZuVydscHObyBCxyvENkJdE4uf+fb7Rmzc5GtsiXayTJmlA4GHkthnxVWSSPOcwJEo+xMEWQ2naHVCeJ9OgCNCYSl66cWHzCvKKyIxZXW1B5NBLcEYgjn79FtUCcMDg+17l6oG43os+vM2XYBA5zPI8FkROEzNJIpKFZRZ5wgEMWWXHn5NfKvtKy4mRjRbLiRhX+oBoJW6qUHFQa9bc2hgIxZMHtGGPM24pwDnKHomogbnPzPGqL1dnrMGpNFityL4nswxtzlLdNh13V2Z7DLhWAxAtNq8gMeUe8UQ3HQwFZcdPn35/UGGkELWA3JHSgbykdh//5HYNwAm4hGbmyurJK3YCKXlTKq6uKK5DHV8RlMeUVHqUYuSQ7VlgZ1XhKPMjjMRnlFjUaCvjDSFEb/PGoGg7GkobNtFwmV26QEYt4nCdg7LQQXpJqeSq/lpl8a2azl6TkgfufMYn+Nya8JDV4puYUgz+5P+0lKc0jd+dT+tB3b77LzM+XFnhJKq3ZlZtiY3dDKzMa+fmGl6TEHttXKT627L06JtwUQV6SGr54+52UvmdjybMpfqbjQQUT8hfc8ZLUWmnkbErv3W46kuLpc9sXU3yt49Jsiu3bCo8w38/UrPSSVMmH/YVJLk9/idDBJp0O0MChKdQmYPLYBuCaAfiLdgiCL2AGU8t0geyvj16etSTbTQcX0rIBAMACJyDSpdMaI0TAAjBwgEJQDjaBV0AjaAcJCHohWBqBtMZI/P/IX/AaShCLqYC9BBwknhad9rxAnuhkxUC9nRV5e5B1CuqrrGrnJRXzvOgUA6PE0cyTxF5Y2HlFr5/QR0+flvblzXzcOd4Lcq+P2I6VxMX8O03srtxO6/iqjOv6sRt/9juOz6UNPbp69Na+lx2zzfb6roUn7Sej5rYVA3Ppz/225vm+W222xw+SP+5c3+2ZqtTOrv5Dc8+5/IdD69x7PJcbbbuGCVdgLqtGOmXuPre5bS72Uf2hnGXpVS1lv1NbXz9Lpd0jpnp+aj8z3TP2N7GeAm5FztJJXPLYJTO99DkPh5AhcNiKsBXxnN0pGPQblQtXb67daqUNJoKmBYN+0NfExu/2jdG0iaAzBYO+7sGXzhMHrg7T0ETQhGDQP7Zw8deVsW/30kETQas3ITMHDf8CYP4eIFwEAAA=", "hash": "5yOLV+mg9ZaXrQPaeT83cTSlv6c=", "signature": "mMrge6WqSImHHjwRHt91LQjsxDhUP+MglnjYnvv2/LY=", "timestamp": "1754215488"}, {"data": "H4sIAAAAAAAAAOM8apvNHfMmVEGAXeouM8fsjfOPsAkJGhpb6BnomRsZmOgZWpjomplIXJj27zCbEmtVhq6zn5YEF1d4Zl5Kfnmxgl+IEJehgZ6BnpGZoYGBUSUXW4WFWbyZicCKn4wSDQ1nLq3/L68kHFxZXJKaqxBQlJ9Smlyi4JeYm6rBaNDAb7GDw4mLg0GAQYrBiMGKwbdbarFTqKrnYqdUBoYG+yxJLh7H0pKM1LySzGRHXxeBA/MusEoIKDBqMDQxMnQxMqxihNq2gZHRi4s3V9kqTvRC26NILy5exwmftok29Nae8uLinbQ9fLOowbpPd7y4eFn5PnwBiwewRDFxMGRxgGy3YHBgaGAEe3EGI8MPxqsKDUz/YYCxg4lxEpOKiaWpebK5iaWuSXKSqa6JoWmKrqVRappuqqmhWaqBoaGJpUnyCqblgpuYehgd6i5xcp7iLNq61azf/NHauqMdDFq3ZxuvdCsxsXqXpVuvVadzVJLnNueGe997LVa9YJ4hIr3icX+0xfMc06SWn4qmm4qUyoQnvGCxfitj0/W4zFhuLtf9Kv1Wz1vBbM+lv7J+tOS+u0jdpdnzfIZx/Swmp+QXQuFmm5Va90eWvShenbRIk50lJNfnM29s9R5e5o9MC+Ifb/sbUR9txM8Z5nvk8gPZWB0BfikmAQEjfs5Jflm6JR+6NggISDEJCBrxc8b/2W25fvrlWQKMUkwCTEb8nOI/T78SKz7bI5AixSSQ+oBR/AWjAAC2zlXJNQIAAA==", "hash": "C4is7V5kDoQ9K0gTEXxMx3sdSgQ=", "signature": "dU91QKZo0VDdlgef4ZwIAiL32T5O93PBxMWQJQ7Bm8U=", "timestamp": "1754215699"}, {"data": "H4sIAAAAAAAAAOM8apvNHfMmVEGAQ+ouM8fsjfOPsAkJGhpb6BnomRsZmOgZWpjomplIXJj27zCbEmtVhq6zn5YEF1d4Zl5Kfnmxgl+IEJehgZ6BnpGZoYGBUSUXW4WFWbyZicCKn4wSDQ1nLq3/L68kHFxZXJKaqxBQlJ9Smlyi4JeYm6rBaNDAb7GDw4mLg0GAQYrBiMGKwbdbarFTqKrnYqdUBoYG+yxJLh7H0pKM1LySzGRHXxeBA/MusEoIKDBqMDQxMnQxMqxihNq2gZHRi4s3V9kqTvRC26NILy5exwmftok29Nae8uLinbQ9fLOowbpPd7y4eFn5PnwBiwewRDFxMGRxgGy3YHBgaGAEe3EGI8MPxqsKDUz/YYCxg4lxEpOKiaWpebK5iaWuSXKSqa6JoWmKrqVRappuqqmhWaqBoaGJpUnyCqblgpuYehgd6i5xcp7iLNq61azf/NHauqMdDFq3ZxuvdCsxsXqXpVuvVadzVJLnNueGe997LVa9YJ4hIr3icX+0xfMc06SWn4qmm4qUyoQnvGCxfitj0/W4zFhuLtf9Kv1Wz1vBbM+lv7J+tOS+u0jdpdnzfIZx/Swmp+QXQuFmm5Va90eWvShenbRIk50lJNfnM29s9R5e5o9M5YtKm6VrFx814ucM8z1y+YFsrI4AvxSTgIARP+ckvyzdkg9dGwQEpJgEBI34OeP/7LZcP/3yLAFGKSYBJiN+TvGfp1+JFZ/tEUiRYhJIfcAo/YJREAAp0H4UNQIAAA==", "hash": "4lBaXzDkU60QbAk3gSOGV5Mgr7E=", "signature": "1fp/E58+zaIKS3tovLieK0LXS2SyxVmkGKy4kDBhS1g=", "timestamp": "1754216283"}, {"data": "H4sIAAAAAAAAAOM8apvNHfMmVEGAU+ouM8fsjfOPsAkJGhpb6BnomRsZmOgZWpjomplIXJj27zCbEmtVhq6zn5YEF1d4Zl5Kfnmxgl+IEJehgZ6BnpGZoYGBUSUXW4WFWbyZicCKn4wSDQ1nLq3/L68kHFxZXJKaqxBQlJ9Smlyi4JeYm6rBaNDAb7GDw4mLg0GAQYrBiMGKwbdbarFTqKrnYqdUBoYG+yxJLh7H0pKM1LySzGRHXxeBA/MusEoIKDBqMDQxMnQxMqxihNq2gZHRi4s3V9kqTvRC26NILy5exwmftok29Nae8uLinbQ9fLOowbpPd7y4eFn5PnwBiwewRDFxMGRxgGy3YHBgaGAEe3EGI8MPxqsKDUz/YYCxg4lxEpOKiaWpebK5iaWuSXKSqa6JoWmKrqVRappuqqmhWaqBoaGJpUnyCqblgpuYehgd6i5xcp7iLNq61azf/NHauqMdDFq3ZxuvdCsxsXqXpVuvVadzVJLnNueGe997LVa9YJ4hIr3icX+0xfMc06SWn4qmm4qUyoQnvGCxfitj0/W4zFhuLtf9Kv1Wz1vBbM+lv7J+tOS+u0jdpdnzfIZx/Swmp+QXQuFmm5Va90eWvShenbRIk50lJNfnM29s9R5e5o9MN7WWaZdsWfHViJ8zzPfI5QeysToC/FJMAgJG/JyT/LJ0Sz50bRAQkGISEDTi54z/s9ty/fTLswQYpZgEmIz4OcV/nn4lVny2RyBFikkg9QGj7AtGIQCWAntwNQIAAA==", "hash": "AjzRZtYWZFOvqNcO7B/vsw3t9kY=", "signature": "XTdvk7Mki59vcfG5sPewL4xe9u0o6C9UZ8ZOUC4FxNs=", "timestamp": "1754217532"}], "last_seen": {"BrowserMetrics": "13398688680562346", "CrashpadMetrics": "13398689883006976"}, "limited_entropy_randomization_source": "910476B99949243006AF7EAF0FD9E953", "log_finalized_record_id": 18, "log_record_id": 31, "low_entropy_source3": 4181, "machine_id": 16508758, "ongoing_logs2": [], "pseudo_low_entropy_source": 2215, "session_id": 10, "stability": {"browser_last_live_timestamp": "13398691593452731", "exited_cleanly": true, "saved_system_profile": "CJuxn8QGEhExMzguMC43MjA0LjE4NC02NBjQlv7DBiIFemgtQ04qGAoKV2luZG93cyBOVBIKMTAuMC4yNjEwMDJ5CgZ4ODZfNjQQqPkBGICAzNKv/x8iE1N5c3RlbSBQcm9kdWN0IE5hbWUoATCADzi4CEIKCAAQABoAMgA6AE2LGqNCVSVJo0JlAACAP2oZCgxBdXRoZW50aWNBTUQQwJ7QBRgQIAEoAIIBAIoBAKoBBng4Nl82NLABAUoKDW0jOl4V0IbiWUoKDUGQ8rYVgI19ykoKDZK3V7MVMK7y3EoKDQUO8PQVgI19ylAEWgIIAGoICAAQADgAQACAAdCW/sMGmAEA+AHVIIAC////////////AYgCAZICJDQ5NTdjNzQ5LTRjYjUtNDE1ZC05MmVmLWU1MTZlMDExNDk0Y6gCpxGyAowBQH7SCQnKCXK1tTaPN+KtfsWIACrbmzOpRnQ0Ou5qLX8qfizFGQzbCbDe9404qugDmBQbqOOPWzjnbDVihPkhNbJyInYTkOgEO+0cPIrjdjMenQrfei+FSdpTBucb9QXxOQvdoidEg0nPaDN/mgJCY+gSVzazIoW/WXboc6tioikHBFRtTPMNXXu8DQPxAviUS54ZviJg", "saved_system_profile_hash": "F810ED623EED865E14B4F2C018B190A149DFFA22", "stats_buildtime": "1753733275", "stats_version": "138.0.7204.184-64", "system_crash_count": 0}, "unsent_log_metadata": {"initial_logs": {"sent_samples_count": 0, "unsent_persisted_size_in_kb": 3, "unsent_samples_count": 4}, "ongoing_logs": {"sent_samples_count": 0, "unsent_persisted_size_in_kb": 0, "unsent_samples_count": 0}}}, "variations_crash_streak": 10, "variations_google_groups": {"Default": []}, "variations_limited_entropy_synthetic_trial_seed_v2": "89", "was": {"restarted": false}}