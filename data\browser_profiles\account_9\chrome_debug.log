[7952:11516:0803/175501.917:INFO:components\enterprise\browser\controller\chrome_browser_cloud_management_controller.cc:202] No machine level policy manager exists.
[7952:11516:0803/175501.936:WARNING:chrome\browser\chrome_browser_main_win.cc:863] Command line too long for RegisterApplicationRestart:  --aggressive-cache-discard --allow-pre-commit-input --disable-async-dns --disable-background-downloads --disable-background-networking --disable-background-timer-throttling --disable-backgrounding-occluded-windows --disable-blink-features=AutomationControlled --disable-client-side-phishing-detection --disable-component-update --disable-default-apps --disable-dev-shm-usage --disable-extensions --disable-gpu-sandbox --disable-hang-monitor --disable-infobars --disable-logging --disable-popup-blocking --disable-prompt-on-repost --disable-ssl-false-start --disable-sync --disable-tls13-early-data --disable-translate --enable-automation --enable-logging --ignore-certificate-errors --ignore-certificate-errors-skip-list --ignore-certificate-errors-spki-list --ignore-ssl-errors --log-level=0 --no-default-browser-check --no-first-run --no-sandbox --no-service-autorun --password-store=basic --proxy-server=http://127.0.0.1:60049 --remote-debugging-port=9222 --silent --test-type=webdriver --use-mock-keychain --user-agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36" --user-data-dir="D:\yaoya\Documents\ceshishi\yyy1 - 副本 - 副本 (9) - 副本 - 副本\data\browser_profiles\account_9" --window-size=1920,1080 --restore-last-session --restart
[7952:11516:0803/175503.977:INFO:CONSOLE:1] "undetected chromedriver 1337!", source:  (1)
[7952:11516:0803/175503.998:WARNING:chrome\browser\media\prefs\capture_device_ranking.h:255] Can't initialize the value of media.audio_input.user_preference_ranking because media.default_audio_capture_device isn't registered or is empty
[7952:11516:0803/175503.998:WARNING:chrome\browser\media\prefs\capture_device_ranking.h:255] Can't initialize the value of media.video_input.user_preference_ranking because media.default_video_capture_Device isn't registered or is empty
[7952:11516:0803/175504.066:INFO:CONSOLE:1] "undetected chromedriver 1337!", source:  (1)
[7952:11516:0803/175504.067:INFO:CONSOLE:1] "undetected chromedriver 1337!", source:  (1)
[19940:13856:0803/175504.098:INFO:third_party\blink\renderer\modules\peerconnection\peer_connection_dependency_factory.cc:802] Running WebRTC with a combined Network and Worker thread.
[7952:11516:0803/175504.875:INFO:CONSOLE:74] "[GSI_LOGGER]: Your client application uses one of the Google One Tap prompt UI status methods that may stop functioning when FedCM becomes mandatory. Refer to the migration guide to update your code accordingly and opt-in to FedCM to test your changes. Learn more: https://developers.google.com/identity/gsi/web/guides/fedcm-migration?s=dc#display_moment and https://developers.google.com/identity/gsi/web/guides/fedcm-migration?s=dc#skipped_moment", source: https://accounts.google.com/gsi/client (74)
[7952:11516:0803/175504.877:INFO:CONSOLE:75] "[GSI_LOGGER]: FedCM get() rejects with TypeError: Failed to execute 'get' on 'CredentialsContainer': Failed to read the 'identity' property from 'CredentialRequestOptions': Failed to read the 'mode' property from 'IdentityCredentialRequestOptions': The provided value 'widget' is not a valid enum value of type IdentityCredentialRequestOptionsMode.", source: https://accounts.google.com/gsi/client (75)
[7952:11516:0803/175504.877:INFO:CONSOLE:75] "[GSI_LOGGER]: FedCM get() rejects with TypeError: Failed to execute 'get' on 'CredentialsContainer': Failed to read the 'identity' property from 'CredentialRequestOptions': Failed to read the 'mode' property from 'IdentityCredentialRequestOptions': The provided value 'widget' is not a valid enum value of type IdentityCredentialRequestOptionsMode.", source: https://accounts.google.com/gsi/client (75)
[7952:11516:0803/175505.013:INFO:CONSOLE:0] "Banner not shown: beforeinstallpromptevent.preventDefault() called. The page must call beforeinstallpromptevent.prompt() to show the banner.", source: https://x.com/ (0)
[7952:11516:0803/175505.163:INFO:CONSOLE:1] "undetected chromedriver 1337!", source:  (1)
[7952:11516:0803/175505.366:INFO:CONSOLE:1] "undetected chromedriver 1337!", source:  (1)
[7952:11516:0803/175505.394:WARNING:chrome\browser\media\prefs\capture_device_ranking.h:255] Can't initialize the value of media.audio_input.user_preference_ranking because media.default_audio_capture_device isn't registered or is empty
[7952:11516:0803/175505.394:WARNING:chrome\browser\media\prefs\capture_device_ranking.h:255] Can't initialize the value of media.video_input.user_preference_ranking because media.default_video_capture_Device isn't registered or is empty
[19940:13856:0803/175505.484:INFO:third_party\blink\renderer\modules\peerconnection\peer_connection_dependency_factory.cc:802] Running WebRTC with a combined Network and Worker thread.
[7952:11516:0803/175505.494:INFO:CONSOLE:1] "undetected chromedriver 1337!", source:  (1)
[7952:11516:0803/175505.497:INFO:CONSOLE:1] "undetected chromedriver 1337!", source:  (1)
[7952:11516:0803/175505.594:INFO:CONSOLE:74] "[GSI_LOGGER]: Your client application uses one of the Google One Tap prompt UI status methods that may stop functioning when FedCM becomes mandatory. Refer to the migration guide to update your code accordingly and opt-in to FedCM to test your changes. Learn more: https://developers.google.com/identity/gsi/web/guides/fedcm-migration?s=dc#display_moment and https://developers.google.com/identity/gsi/web/guides/fedcm-migration?s=dc#skipped_moment", source: https://accounts.google.com/gsi/client (74)
[7952:11516:0803/175505.595:INFO:CONSOLE:75] "[GSI_LOGGER]: FedCM get() rejects with TypeError: Failed to execute 'get' on 'CredentialsContainer': Failed to read the 'identity' property from 'CredentialRequestOptions': Failed to read the 'mode' property from 'IdentityCredentialRequestOptions': The provided value 'widget' is not a valid enum value of type IdentityCredentialRequestOptionsMode.", source: https://accounts.google.com/gsi/client (75)
[7952:11516:0803/175505.595:INFO:CONSOLE:75] "[GSI_LOGGER]: FedCM get() rejects with TypeError: Failed to execute 'get' on 'CredentialsContainer': Failed to read the 'identity' property from 'CredentialRequestOptions': Failed to read the 'mode' property from 'IdentityCredentialRequestOptions': The provided value 'widget' is not a valid enum value of type IdentityCredentialRequestOptionsMode.", source: https://accounts.google.com/gsi/client (75)
[7952:11516:0803/175505.762:INFO:CONSOLE:0] "Banner not shown: beforeinstallpromptevent.preventDefault() called. The page must call beforeinstallpromptevent.prompt() to show the banner.", source: https://x.com/ (0)
[7952:11516:0803/175506.688:INFO:CONSOLE:1] "undetected chromedriver 1337!", source:  (1)
[19940:36444:0803/175507.003:WARNING:third_party\webrtc\media\engine\webrtc_voice_engine.cc:807] Attempting to stop aecdump when no audio processing module is present
[7952:11516:0803/175507.957:INFO:CONSOLE:1] "undetected chromedriver 1337!", source:  (1)
[7952:11516:0803/175508.205:INFO:CONSOLE:1] "undetected chromedriver 1337!", source:  (1)
[7952:11516:0803/175508.234:WARNING:chrome\browser\media\prefs\capture_device_ranking.h:255] Can't initialize the value of media.audio_input.user_preference_ranking because media.default_audio_capture_device isn't registered or is empty
[7952:11516:0803/175508.234:WARNING:chrome\browser\media\prefs\capture_device_ranking.h:255] Can't initialize the value of media.video_input.user_preference_ranking because media.default_video_capture_Device isn't registered or is empty
[19940:13856:0803/175508.311:INFO:third_party\blink\renderer\modules\peerconnection\peer_connection_dependency_factory.cc:802] Running WebRTC with a combined Network and Worker thread.
[7952:11516:0803/175508.321:INFO:CONSOLE:1] "undetected chromedriver 1337!", source:  (1)
[7952:11516:0803/175508.323:INFO:CONSOLE:1] "undetected chromedriver 1337!", source:  (1)
[33120:36036:0803/175508.838:WARNING:net\spdy\spdy_session.cc:3054] Received HEADERS for invalid stream 45
[7952:11516:0803/175508.842:INFO:CONSOLE:1] "undetected chromedriver 1337!", source:  (1)
[33120:36036:0803/175508.857:WARNING:net\spdy\spdy_session.cc:3054] Received HEADERS for invalid stream 1
[33120:36036:0803/175508.869:WARNING:net\spdy\spdy_session.cc:3054] Received HEADERS for invalid stream 23
[33120:36036:0803/175508.880:WARNING:net\spdy\spdy_session.cc:3054] Received HEADERS for invalid stream 47
[33120:36036:0803/175508.884:WARNING:net\spdy\spdy_session.cc:3054] Received HEADERS for invalid stream 69
[33120:3216:0803/175508.938:WARNING:net\disk_cache\blockfile\backend_impl.cc:1762] Destroying invalid entry.
[33120:36036:0803/175508.987:WARNING:net\spdy\spdy_session.cc:3054] Received HEADERS for invalid stream 67
[33120:36036:0803/175509.056:WARNING:net\spdy\spdy_session.cc:3054] Received HEADERS for invalid stream 61
[7952:11516:0803/175509.130:INFO:CONSOLE:1] "undetected chromedriver 1337!", source:  (1)
[7952:11516:0803/175509.157:WARNING:chrome\browser\media\prefs\capture_device_ranking.h:255] Can't initialize the value of media.audio_input.user_preference_ranking because media.default_audio_capture_device isn't registered or is empty
[7952:11516:0803/175509.157:WARNING:chrome\browser\media\prefs\capture_device_ranking.h:255] Can't initialize the value of media.video_input.user_preference_ranking because media.default_video_capture_Device isn't registered or is empty
[19940:13856:0803/175509.239:INFO:third_party\blink\renderer\modules\peerconnection\peer_connection_dependency_factory.cc:802] Running WebRTC with a combined Network and Worker thread.
[7952:11516:0803/175509.249:INFO:CONSOLE:1] "undetected chromedriver 1337!", source:  (1)
[7952:11516:0803/175509.251:INFO:CONSOLE:1] "undetected chromedriver 1337!", source:  (1)
[7952:11516:0803/175509.266:INFO:CONSOLE:1] "Error", source: https://abs.twimg.com/responsive-web/client-web/main.69d10e7a.js (1)
[19940:36444:0803/175512.651:WARNING:third_party\webrtc\media\engine\webrtc_voice_engine.cc:807] Attempting to stop aecdump when no audio processing module is present
[19940:36444:0803/175512.651:WARNING:third_party\webrtc\media\engine\webrtc_voice_engine.cc:807] Attempting to stop aecdump when no audio processing module is present
[7952:11516:0803/175513.356:INFO:CONSOLE:6] "🎯 开始模拟真实用户输入，内容: 👤 真实用户输入测试

这应该能激活发布按钮 ✅", source:  (6)
[7952:11516:0803/175513.357:INFO:CONSOLE:21] "找到文本框: [object HTMLDivElement]", source:  (21)
[7952:11516:0803/175513.364:INFO:CONSOLE:1] "The AudioContext was not allowed to start. It must be resumed (or created) after a user gesture on the page. https://developer.chrome.com/blog/autoplay/#web_audio", source: https://abs.twimg.com/responsive-web/client-web/shared~loader.AudioDock~loader.DashMenu~loader.DashModal~loader.DMDrawer~ondemand.InlinePlayer~ondem-c7e58553.33fca9aa.js (1)
[7952:11516:0803/175516.257:INFO:CONSOLE:116] "模拟输入最终内容: 👤� ", source:  (116)
[7952:11516:0803/175516.257:INFO:CONSOLE:117] "内容长度: 4", source:  (117)
[7952:11516:0803/175516.257:INFO:CONSOLE:122] "发布按钮状态: 禁用", source:  (122)
[7952:11516:0803/175517.612:INFO:CONSOLE:1] "undetected chromedriver 1337!", source:  (1)
[7952:11516:0803/175517.846:INFO:CONSOLE:1] "undetected chromedriver 1337!", source:  (1)
[7952:11516:0803/175517.875:WARNING:chrome\browser\media\prefs\capture_device_ranking.h:255] Can't initialize the value of media.audio_input.user_preference_ranking because media.default_audio_capture_device isn't registered or is empty
[7952:11516:0803/175517.875:WARNING:chrome\browser\media\prefs\capture_device_ranking.h:255] Can't initialize the value of media.video_input.user_preference_ranking because media.default_video_capture_Device isn't registered or is empty
[19940:13856:0803/175517.958:INFO:third_party\blink\renderer\modules\peerconnection\peer_connection_dependency_factory.cc:802] Running WebRTC with a combined Network and Worker thread.
[7952:11516:0803/175517.968:INFO:CONSOLE:1] "undetected chromedriver 1337!", source:  (1)
[7952:11516:0803/175517.970:INFO:CONSOLE:1] "undetected chromedriver 1337!", source:  (1)
[7952:32564:0803/175519.413:ERROR:google_apis\gcm\engine\registration_request.cc:291] Registration response error message: DEPRECATED_ENDPOINT
[7952:11516:0803/175520.324:INFO:CONSOLE:1] "undetected chromedriver 1337!", source:  (1)
[33120:36036:0803/175520.453:WARNING:net\spdy\spdy_session.cc:3054] Received HEADERS for invalid stream 117
[33120:36036:0803/175520.453:WARNING:net\spdy\spdy_session.cc:3054] Received HEADERS for invalid stream 119
[33120:36036:0803/175520.780:WARNING:net\spdy\spdy_session.cc:3054] Received HEADERS for invalid stream 121
[7952:11516:0803/175520.862:INFO:CONSOLE:1] "undetected chromedriver 1337!", source:  (1)
[7952:11516:0803/175520.888:WARNING:chrome\browser\media\prefs\capture_device_ranking.h:255] Can't initialize the value of media.audio_input.user_preference_ranking because media.default_audio_capture_device isn't registered or is empty
[7952:11516:0803/175520.888:WARNING:chrome\browser\media\prefs\capture_device_ranking.h:255] Can't initialize the value of media.video_input.user_preference_ranking because media.default_video_capture_Device isn't registered or is empty
[33120:36036:0803/175520.979:WARNING:net\spdy\spdy_session.cc:3054] Received HEADERS for invalid stream 123
[7952:11516:0803/175520.982:INFO:CONSOLE:1] "undetected chromedriver 1337!", source:  (1)
[7952:11516:0803/175520.984:INFO:CONSOLE:1] "undetected chromedriver 1337!", source:  (1)
[19940:13856:0803/175521.004:INFO:third_party\blink\renderer\modules\peerconnection\peer_connection_dependency_factory.cc:802] Running WebRTC with a combined Network and Worker thread.
[7952:11516:0803/175521.015:INFO:CONSOLE:1] "Error", source: https://abs.twimg.com/responsive-web/client-web/main.69d10e7a.js (1)
[19940:36444:0803/175521.087:WARNING:third_party\webrtc\media\engine\webrtc_voice_engine.cc:807] Attempting to stop aecdump when no audio processing module is present
[19940:36444:0803/175522.856:WARNING:third_party\webrtc\media\engine\webrtc_voice_engine.cc:807] Attempting to stop aecdump when no audio processing module is present
[7952:11516:0803/175523.639:INFO:CONSOLE:1] "undetected chromedriver 1337!", source:  (1)
[33120:36036:0803/175523.779:WARNING:net\spdy\spdy_session.cc:3054] Received HEADERS for invalid stream 129
[7952:11516:0803/175523.805:INFO:CONSOLE:1] "undetected chromedriver 1337!", source:  (1)
[7952:11516:0803/175523.833:WARNING:chrome\browser\media\prefs\capture_device_ranking.h:255] Can't initialize the value of media.audio_input.user_preference_ranking because media.default_audio_capture_device isn't registered or is empty
[7952:11516:0803/175523.833:WARNING:chrome\browser\media\prefs\capture_device_ranking.h:255] Can't initialize the value of media.video_input.user_preference_ranking because media.default_video_capture_Device isn't registered or is empty
[7952:11516:0803/175523.909:INFO:CONSOLE:1] "undetected chromedriver 1337!", source:  (1)
[7952:11516:0803/175523.910:INFO:CONSOLE:1] "undetected chromedriver 1337!", source:  (1)
[19940:13856:0803/175523.927:INFO:third_party\blink\renderer\modules\peerconnection\peer_connection_dependency_factory.cc:802] Running WebRTC with a combined Network and Worker thread.
[7952:11516:0803/175523.937:INFO:CONSOLE:1] "Error", source: https://abs.twimg.com/responsive-web/client-web/main.69d10e7a.js (1)
[7952:11516:0803/175526.269:INFO:CONSOLE:1] "undetected chromedriver 1337!", source:  (1)
[33120:36036:0803/175526.332:WARNING:net\spdy\spdy_session.cc:3054] Received HEADERS for invalid stream 145
[33120:36036:0803/175526.446:WARNING:net\spdy\spdy_session.cc:3054] Received HEADERS for invalid stream 147
[7952:11516:0803/175526.524:INFO:CONSOLE:1] "undetected chromedriver 1337!", source:  (1)
[7952:11516:0803/175526.551:WARNING:chrome\browser\media\prefs\capture_device_ranking.h:255] Can't initialize the value of media.audio_input.user_preference_ranking because media.default_audio_capture_device isn't registered or is empty
[7952:11516:0803/175526.551:WARNING:chrome\browser\media\prefs\capture_device_ranking.h:255] Can't initialize the value of media.video_input.user_preference_ranking because media.default_video_capture_Device isn't registered or is empty
[7952:11516:0803/175526.629:INFO:CONSOLE:1] "undetected chromedriver 1337!", source:  (1)
[7952:11516:0803/175526.631:INFO:CONSOLE:1] "undetected chromedriver 1337!", source:  (1)
[19940:13856:0803/175526.650:INFO:third_party\blink\renderer\modules\peerconnection\peer_connection_dependency_factory.cc:802] Running WebRTC with a combined Network and Worker thread.
[19940:36444:0803/175526.691:WARNING:third_party\webrtc\media\engine\webrtc_voice_engine.cc:807] Attempting to stop aecdump when no audio processing module is present
[33120:36036:0803/175529.323:WARNING:net\spdy\spdy_session.cc:3054] Received HEADERS for invalid stream 153
[19940:36444:0803/175534.905:WARNING:third_party\webrtc\media\engine\webrtc_voice_engine.cc:807] Attempting to stop aecdump when no audio processing module is present
[7952:32564:0803/175542.142:ERROR:google_apis\gcm\engine\registration_request.cc:291] Registration response error message: DEPRECATED_ENDPOINT
[7952:32564:0803/175640.131:ERROR:google_apis\gcm\engine\registration_request.cc:291] Registration response error message: DEPRECATED_ENDPOINT
