#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🤖 无头模式表情符号输入修复验证脚本
验证无头模式下的表情符号输入功能
"""

import asyncio
import logging
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from src.core.anti_detection import AntiDetectionEngine

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

class MockDriverWrapper:
    """模拟的驱动包装器"""
    def __init__(self, driver):
        self.driver = driver
    
    async def execute_async(self, func):
        """异步执行函数"""
        return func()
    
    async def find_elements(self, by, selector):
        """查找元素"""
        return self.driver.find_elements(by, selector)

async def test_headless_emoji_input():
    """测试无头模式下的表情符号输入"""
    print("🤖 开始测试无头模式表情符号输入...")
    
    # 设置Chrome无头模式
    chrome_options = Options()
    chrome_options.add_argument('--headless')
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_argument('--disable-gpu')
    
    driver = None
    try:
        # 创建WebDriver
        driver = webdriver.Chrome(options=chrome_options)
        driver_wrapper = MockDriverWrapper(driver)
        
        # 创建测试页面
        test_html = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>表情符号输入测试</title>
            <meta charset="UTF-8">
        </head>
        <body>
            <h1>表情符号输入测试</h1>
            <textarea id="test-textarea" placeholder="在这里输入表情符号..." style="width: 500px; height: 200px;"></textarea>
            <br><br>
            <div id="test-contenteditable" contenteditable="true" style="border: 1px solid #ccc; width: 500px; height: 200px; padding: 10px;" placeholder="contenteditable测试区域"></div>
            <br><br>
            <input type="text" id="test-input" placeholder="文本输入框测试" style="width: 500px; height: 40px;">
        </body>
        </html>
        """
        
        # 加载测试页面
        driver.get("data:text/html;charset=utf-8," + test_html)
        await asyncio.sleep(1)
        
        # 创建反检测引擎
        anti_detection = AntiDetectionEngine()
        behavior_simulator = anti_detection.behavior_simulator
        
        # 测试用例
        test_cases = [
            {
                'name': 'Textarea元素',
                'selector': '#test-textarea',
                'text': '💖 😎 😇 ❤️\n\n又是美好的一天，在线中....\n\n👍🔥'
            },
            {
                'name': 'ContentEditable元素',
                'selector': '#test-contenteditable',
                'text': '🌸 👍 🥰 😊\n\n测试内容编辑区域\n\n🤩😍'
            },
            {
                'name': 'Input元素',
                'selector': '#test-input',
                'text': '✨🥰 简单测试 🔥🎊'
            }
        ]
        
        results = []
        
        for test_case in test_cases:
            print(f"\n{'='*50}")
            print(f"测试: {test_case['name']}")
            print(f"选择器: {test_case['selector']}")
            print(f"测试文本: {test_case['text']}")
            print('='*50)
            
            try:
                # 查找元素
                element = driver.find_element(By.CSS_SELECTOR, test_case['selector'])
                
                # 测试无头模式JavaScript输入
                print("🔄 测试无头模式JavaScript输入...")
                success1 = await behavior_simulator._headless_js_input(
                    driver_wrapper, element, test_case['text']
                )
                
                if success1:
                    # 验证输入结果
                    actual_content = driver.execute_script(
                        'return arguments[0].value || arguments[0].textContent || arguments[0].innerText || "";',
                        element
                    )
                    print(f"✅ JavaScript输入成功")
                    print(f"   期望长度: {len(test_case['text'])}")
                    print(f"   实际长度: {len(actual_content)}")
                    print(f"   实际内容: '{actual_content[:100]}...'")
                    
                    results.append({
                        'test': test_case['name'],
                        'method': 'JavaScript',
                        'success': True,
                        'expected_length': len(test_case['text']),
                        'actual_length': len(actual_content),
                        'content_match': test_case['text'].replace('\n', '') in actual_content.replace('\n', '')
                    })
                else:
                    print("❌ JavaScript输入失败")
                    results.append({
                        'test': test_case['name'],
                        'method': 'JavaScript',
                        'success': False
                    })
                
                # 清空元素准备下一个测试
                driver.execute_script(
                    "arguments[0].value = ''; arguments[0].textContent = ''; arguments[0].innerText = '';",
                    element
                )
                
                # 测试无头模式分段输入
                print("🔄 测试无头模式分段输入...")
                success2 = await behavior_simulator._headless_segmented_input(
                    driver_wrapper, element, test_case['text']
                )
                
                if success2:
                    actual_content = driver.execute_script(
                        'return arguments[0].value || arguments[0].textContent || arguments[0].innerText || "";',
                        element
                    )
                    print(f"✅ 分段输入成功")
                    print(f"   期望长度: {len(test_case['text'])}")
                    print(f"   实际长度: {len(actual_content)}")
                    print(f"   实际内容: '{actual_content[:100]}...'")
                    
                    results.append({
                        'test': test_case['name'],
                        'method': 'Segmented',
                        'success': True,
                        'expected_length': len(test_case['text']),
                        'actual_length': len(actual_content),
                        'content_match': test_case['text'].replace('\n', '') in actual_content.replace('\n', '')
                    })
                else:
                    print("❌ 分段输入失败")
                    results.append({
                        'test': test_case['name'],
                        'method': 'Segmented',
                        'success': False
                    })
                    
            except Exception as e:
                print(f"❌ 测试异常: {e}")
                results.append({
                    'test': test_case['name'],
                    'method': 'Both',
                    'success': False,
                    'error': str(e)
                })
        
        # 输出测试结果总结
        print(f"\n{'='*60}")
        print("🎯 测试结果总结")
        print('='*60)
        
        total_tests = len(results)
        successful_tests = len([r for r in results if r.get('success', False)])
        
        for result in results:
            status = "✅" if result.get('success', False) else "❌"
            test_name = result['test']
            method = result['method']
            
            if result.get('success', False):
                content_match = "✅" if result.get('content_match', False) else "⚠️"
                print(f"{status} {test_name} - {method}: 长度 {result.get('actual_length', 0)}/{result.get('expected_length', 0)} {content_match}")
            else:
                error = result.get('error', '输入失败')
                print(f"{status} {test_name} - {method}: {error}")
        
        print(f"\n总体成功率: {successful_tests}/{total_tests} ({successful_tests/total_tests*100:.1f}%)")
        
        if successful_tests == total_tests:
            print("🎉 所有测试通过！无头模式表情符号输入修复成功！")
        else:
            print("⚠️ 部分测试失败，需要进一步优化")
            
    except Exception as e:
        print(f"❌ 测试过程异常: {e}")
        
    finally:
        if driver:
            driver.quit()

def main():
    """主函数"""
    print("🤖 无头模式表情符号输入修复验证")
    print("="*60)
    
    try:
        asyncio.run(test_headless_emoji_input())
    except Exception as e:
        print(f"❌ 测试运行失败: {e}")

if __name__ == "__main__":
    main()
