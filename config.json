{"database": {"url": "sqlite:///data/database/app.db", "pool_size": 10, "max_overflow": 20}, "browser": {"headless": true, "max_browsers": 10, "timeout": 30000, "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"}, "auto_cleanup": {"enabled": true, "interval_hours": 6, "max_directory_size_mb": 500, "cache_max_age_hours": 24}, "logging": {"level": "INFO", "max_size": "10MB", "backup_count": 5, "rotation": "1 day"}, "monitoring": {"enabled": true, "memory_threshold": 80, "disk_threshold": 90, "alert_interval": 300}}